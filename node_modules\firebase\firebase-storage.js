import{_getProvider,getApp as e,_registerComponent as t,registerVersion as n,_isFirebaseServerApp as r,SDK_VERSION as o}from"https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";const stringToByteArray$1=function(e){const t=[];let n=0;for(let r=0;r<e.length;r++){let o=e.charCodeAt(r);o<128?t[n++]=o:o<2048?(t[n++]=o>>6|192,t[n++]=63&o|128):55296==(64512&o)&&r+1<e.length&&56320==(64512&e.charCodeAt(r+1))?(o=65536+((1023&o)<<10)+(1023&e.charCodeAt(++r)),t[n++]=o>>18|240,t[n++]=o>>12&63|128,t[n++]=o>>6&63|128,t[n++]=63&o|128):(t[n++]=o>>12|224,t[n++]=o>>6&63|128,t[n++]=63&o|128)}return t},s={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let t=0;t<e.length;t+=3){const o=e[t],s=t+1<e.length,i=s?e[t+1]:0,a=t+2<e.length,l=a?e[t+2]:0,c=o>>2,u=(3&o)<<4|i>>4;let h=(15&i)<<2|l>>6,d=63&l;a||(d=64,s||(h=64)),r.push(n[c],n[u],n[h],n[d])}return r.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(stringToByteArray$1(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let n=0,r=0;for(;n<e.length;){const o=e[n++];if(o<128)t[r++]=String.fromCharCode(o);else if(o>191&&o<224){const s=e[n++];t[r++]=String.fromCharCode((31&o)<<6|63&s)}else if(o>239&&o<365){const s=((7&o)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++])-65536;t[r++]=String.fromCharCode(55296+(s>>10)),t[r++]=String.fromCharCode(56320+(1023&s))}else{const s=e[n++],i=e[n++];t[r++]=String.fromCharCode((15&o)<<12|(63&s)<<6|63&i)}}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();const n=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let t=0;t<e.length;){const o=n[e.charAt(t++)],s=t<e.length?n[e.charAt(t)]:0;++t;const i=t<e.length?n[e.charAt(t)]:64;++t;const a=t<e.length?n[e.charAt(t)]:64;if(++t,null==o||null==s||null==i||null==a)throw new DecodeBase64StringError;const l=o<<2|s>>4;if(r.push(l),64!==i){const e=s<<4&240|i>>2;if(r.push(e),64!==a){const e=i<<6&192|a;r.push(e)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class DecodeBase64StringError extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const base64urlEncodeWithoutPadding=function(e){return function(e){const t=stringToByteArray$1(e);return s.encodeByteArray(t,!0)}(e).replace(/\./g,"")};const getDefaultsFromGlobal=()=>function getGlobal(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__,getDefaultsFromCookie=()=>{if("undefined"==typeof document)return;let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}const t=e&&function(e){try{return s.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null}(e[1]);return t&&JSON.parse(t)},getDefaults=()=>{try{return getDefaultsFromGlobal()||(()=>{if("undefined"==typeof process||void 0===process.env)return;const e=process.env.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0})()||getDefaultsFromCookie()}catch(e){return void console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`)}},getDefaultEmulatorHostnameAndPort=e=>{const t=(e=>getDefaults()?.emulatorHosts?.[e])(e);if(!t)return;const n=t.lastIndexOf(":");if(n<=0||n+1===t.length)throw new Error(`Invalid host ${t} with no separate hostname and port!`);const r=parseInt(t.substring(n+1),10);return"["===t[0]?[t.substring(1,n-1),r]:[t.substring(0,n),r]};function isCloudWorkstation(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch{return!1}}const i={};let a=!1;function updateEmulatorBanner(e,t){if("undefined"==typeof window||"undefined"==typeof document||!isCloudWorkstation(window.location.host)||i[e]===t||i[e]||a)return;function prefixedId(e){return`__firebase__banner__${e}`}i[e]=t;const n="__firebase__banner",r=function getEmulatorSummary(){const e={prod:[],emulator:[]};for(const t of Object.keys(i))i[t]?e.emulator.push(t):e.prod.push(t);return e}().prod.length>0;function setupCloseBtn(){const e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{a=!0,function tearDown(){const e=document.getElementById(n);e&&e.remove()}()},e}function setupDom(){const e=function getOrCreateEl(e){let t=document.getElementById(e),n=!1;return t||(t=document.createElement("div"),t.setAttribute("id",e),n=!0),{created:n,element:t}}(n),t=prefixedId("text"),o=document.getElementById(t)||document.createElement("span"),s=prefixedId("learnmore"),i=document.getElementById(s)||document.createElement("a"),a=prefixedId("preprendIcon"),l=document.getElementById(a)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(e.created){const t=e.element;!function setupBannerStyles(e){e.style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center"}(t),function setupLinkStyles(e,t){e.setAttribute("id",t),e.innerText="Learn more",e.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",e.setAttribute("target","__blank"),e.style.paddingLeft="5px",e.style.textDecoration="underline"}(i,s);const n=setupCloseBtn();!function setupIconStyles(e,t){e.setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px"}(l,a),t.append(l,o,i,n),document.body.appendChild(t)}r?(o.innerText="Preview backend disconnected.",l.innerHTML='<g clip-path="url(#clip0_6013_33858)">\n<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6013_33858">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>'):(l.innerHTML='<g clip-path="url(#clip0_6083_34804)">\n<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6083_34804">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>',o.innerText="Preview backend running in this workspace."),o.setAttribute("id",t)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",setupDom):setupDom()}class FirebaseError extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){const n=t[0]||{},r=`${this.service}/${e}`,o=this.errors[e],s=o?function replaceTemplate(e,t){return e.replace(l,((e,n)=>{const r=t[n];return null!=r?String(r):`<${n}?>`}))}(o,n):"Error",i=`${this.serviceName}: ${s} (${r}).`;return new FirebaseError(r,i,n)}}const l=/\{\$([^}]+)}/g;function getModularInstance(e){return e&&e._delegate?e._delegate:e}class Component{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const c="firebasestorage.googleapis.com",u="storageBucket";class StorageError extends FirebaseError{constructor(e,t,n=0){super(prependCode(e),`Firebase Storage: ${t} (${prependCode(e)})`),this.status_=n,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,StorageError.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return prependCode(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}\n${this.customData.serverResponse}`:this.message=this._baseMessage}}var h,d;function prependCode(e){return"storage/"+e}function unknown(){return new StorageError(h.UNKNOWN,"An unknown error occurred, please check the error payload for server response.")}function retryLimitExceeded(){return new StorageError(h.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function canceled(){return new StorageError(h.CANCELED,"User canceled the upload/download.")}function cannotSliceBlob(){return new StorageError(h.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function invalidArgument(e){return new StorageError(h.INVALID_ARGUMENT,e)}function appDeleted(){return new StorageError(h.APP_DELETED,"The Firebase app was deleted.")}function invalidRootOperation(e){return new StorageError(h.INVALID_ROOT_OPERATION,"The operation '"+e+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function invalidFormat(e,t){return new StorageError(h.INVALID_FORMAT,"String does not match format '"+e+"': "+t)}function internalError(e){throw new StorageError(h.INTERNAL_ERROR,"Internal error: "+e)}!function(e){e.UNKNOWN="unknown",e.OBJECT_NOT_FOUND="object-not-found",e.BUCKET_NOT_FOUND="bucket-not-found",e.PROJECT_NOT_FOUND="project-not-found",e.QUOTA_EXCEEDED="quota-exceeded",e.UNAUTHENTICATED="unauthenticated",e.UNAUTHORIZED="unauthorized",e.UNAUTHORIZED_APP="unauthorized-app",e.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",e.INVALID_CHECKSUM="invalid-checksum",e.CANCELED="canceled",e.INVALID_EVENT_NAME="invalid-event-name",e.INVALID_URL="invalid-url",e.INVALID_DEFAULT_BUCKET="invalid-default-bucket",e.NO_DEFAULT_BUCKET="no-default-bucket",e.CANNOT_SLICE_BLOB="cannot-slice-blob",e.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",e.NO_DOWNLOAD_URL="no-download-url",e.INVALID_ARGUMENT="invalid-argument",e.INVALID_ARGUMENT_COUNT="invalid-argument-count",e.APP_DELETED="app-deleted",e.INVALID_ROOT_OPERATION="invalid-root-operation",e.INVALID_FORMAT="invalid-format",e.INTERNAL_ERROR="internal-error",e.UNSUPPORTED_ENVIRONMENT="unsupported-environment"}(h||(h={}));class Location{constructor(e,t){this.bucket=e,this.path_=t}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){const e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(e,t){let n;try{n=Location.makeFromUrl(e,t)}catch(t){return new Location(e,"")}if(""===n.path)return n;throw function invalidDefaultBucket(e){return new StorageError(h.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+e+"'.")}(e)}static makeFromUrl(e,t){let n=null;const r="([A-Za-z0-9.\\-_]+)";const o=new RegExp("^gs://"+r+"(/(.*))?$","i");function httpModify(e){e.path_=decodeURIComponent(e.path)}const s=t.replace(/[.]/g,"\\."),i=[{regex:o,indices:{bucket:1,path:3},postModify:function gsModify(e){"/"===e.path.charAt(e.path.length-1)&&(e.path_=e.path_.slice(0,-1))}},{regex:new RegExp(`^https?://${s}/v[A-Za-z0-9_]+/b/${r}/o(/([^?#]*).*)?$`,"i"),indices:{bucket:1,path:3},postModify:httpModify},{regex:new RegExp(`^https?://${t===c?"(?:storage.googleapis.com|storage.cloud.google.com)":t}/${r}/([^?#]*)`,"i"),indices:{bucket:1,path:2},postModify:httpModify}];for(let t=0;t<i.length;t++){const r=i[t],o=r.regex.exec(e);if(o){const e=o[r.indices.bucket];let t=o[r.indices.path];t||(t=""),n=new Location(e,t),r.postModify(n);break}}if(null==n)throw function invalidUrl(e){return new StorageError(h.INVALID_URL,"Invalid URL '"+e+"'.")}(e);return n}}class FailRequest{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=!1){}}function isString(e){return"string"==typeof e||e instanceof String}function isNativeBlob(e){return isNativeBlobDefined()&&e instanceof Blob}function isNativeBlobDefined(){return"undefined"!=typeof Blob}function validateNumber(e,t,n,r){if(r<t)throw invalidArgument(`Invalid value for '${e}'. Expected ${t} or greater.`);if(r>n)throw invalidArgument(`Invalid value for '${e}'. Expected ${n} or less.`)}function makeUrl(e,t,n){let r=t;return null==n&&(r=`https://${t}`),`${n}://${r}/v0${e}`}function makeQueryString(e){const t=encodeURIComponent;let n="?";for(const r in e)if(e.hasOwnProperty(r)){n=n+(t(r)+"="+t(e[r]))+"&"}return n=n.slice(0,-1),n}function isRetryStatusCode(e,t){const n=e>=500&&e<600,r=-1!==[408,429].indexOf(e),o=-1!==t.indexOf(e);return n||r||o}!function(e){e[e.NO_ERROR=0]="NO_ERROR",e[e.NETWORK_ERROR=1]="NETWORK_ERROR",e[e.ABORT=2]="ABORT"}(d||(d={}));class NetworkRequest{constructor(e,t,n,r,o,s,i,a,l,c,u,h=!0,d=!1){this.url_=e,this.method_=t,this.headers_=n,this.body_=r,this.successCodes_=o,this.additionalRetryCodes_=s,this.callback_=i,this.errorCallback_=a,this.timeout_=l,this.progressCallback_=c,this.connectionFactory_=u,this.retry=h,this.isUsingEmulator=d,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise(((e,t)=>{this.resolve_=e,this.reject_=t,this.start_()}))}start_(){const doTheRequest=(e,t)=>{if(t)return void e(!1,new RequestEndStatus(!1,null,!0));const n=this.connectionFactory_();this.pendingConnection_=n;const progressListener=e=>{const t=e.loaded,n=e.lengthComputable?e.total:-1;null!==this.progressCallback_&&this.progressCallback_(t,n)};null!==this.progressCallback_&&n.addUploadProgressListener(progressListener),n.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then((()=>{null!==this.progressCallback_&&n.removeUploadProgressListener(progressListener),this.pendingConnection_=null;const t=n.getErrorCode()===d.NO_ERROR,r=n.getStatus();if(!t||isRetryStatusCode(r,this.additionalRetryCodes_)&&this.retry){const t=n.getErrorCode()===d.ABORT;return void e(!1,new RequestEndStatus(!1,null,t))}const o=-1!==this.successCodes_.indexOf(r);e(!0,new RequestEndStatus(o,n))}))},backoffDone=(e,t)=>{const n=this.resolve_,r=this.reject_,o=t.connection;if(t.wasSuccessCode)try{const e=this.callback_(o,o.getResponse());!function isJustDef(e){return void 0!==e}(e)?n():n(e)}catch(e){r(e)}else if(null!==o){const e=unknown();e.serverResponse=o.getErrorText(),this.errorCallback_?r(this.errorCallback_(o,e)):r(e)}else if(t.canceled){r(this.appDelete_?appDeleted():canceled())}else{r(retryLimitExceeded())}};this.canceled_?backoffDone(0,new RequestEndStatus(!1,null,!0)):this.backoffId_=function start(e,t,n){let r=1,o=null,s=null,i=!1,a=0;function canceled(){return 2===a}let l=!1;function triggerCallback(...e){l||(l=!0,t.apply(null,e))}function callWithDelay(t){o=setTimeout((()=>{o=null,e(responseHandler,canceled())}),t)}function clearGlobalTimeout(){s&&clearTimeout(s)}function responseHandler(e,...t){if(l)return void clearGlobalTimeout();if(e)return clearGlobalTimeout(),void triggerCallback.call(null,e,...t);if(canceled()||i)return clearGlobalTimeout(),void triggerCallback.call(null,e,...t);let n;r<64&&(r*=2),1===a?(a=2,n=0):n=1e3*(r+Math.random()),callWithDelay(n)}let c=!1;function stop(e){c||(c=!0,clearGlobalTimeout(),l||(null!==o?(e||(a=2),clearTimeout(o),callWithDelay(0)):e||(a=1)))}return callWithDelay(0),s=setTimeout((()=>{i=!0,stop(!0)}),n),stop}(doTheRequest,backoffDone,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,null!==this.backoffId_&&function stop(e){e(!1)}(this.backoffId_),null!==this.pendingConnection_&&this.pendingConnection_.abort()}}class RequestEndStatus{constructor(e,t,n){this.wasSuccessCode=e,this.connection=t,this.canceled=!!n}}function getBlobBuilder(){return"undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:void 0}function getBlob$1(...e){const t=getBlobBuilder();if(void 0!==t){const n=new t;for(let t=0;t<e.length;t++)n.append(e[t]);return n.getBlob()}if(isNativeBlobDefined())return new Blob(e);throw new StorageError(h.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}function decodeBase64(e){if("undefined"==typeof atob)throw function missingPolyFill(e){return new StorageError(h.UNSUPPORTED_ENVIRONMENT,`${e} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`)}("base-64");return atob(e)}const p={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"};class StringData{constructor(e,t){this.data=e,this.contentType=t||null}}function dataFromString(e,t){switch(e){case p.RAW:return new StringData(utf8Bytes_(t));case p.BASE64:case p.BASE64URL:return new StringData(base64Bytes_(e,t));case p.DATA_URL:return new StringData(function dataURLBytes_(e){const t=new DataURLParts(e);return t.base64?base64Bytes_(p.BASE64,t.rest):function percentEncodedBytes_(e){let t;try{t=decodeURIComponent(e)}catch(e){throw invalidFormat(p.DATA_URL,"Malformed data URL.")}return utf8Bytes_(t)}(t.rest)}(t),function dataURLContentType_(e){return new DataURLParts(e).contentType}(t))}throw unknown()}function utf8Bytes_(e){const t=[];for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);if(r<=127)t.push(r);else if(r<=2047)t.push(192|r>>6,128|63&r);else if(55296==(64512&r)){if(n<e.length-1&&56320==(64512&e.charCodeAt(n+1))){r=65536|(1023&r)<<10|1023&e.charCodeAt(++n),t.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r)}else t.push(239,191,189)}else 56320==(64512&r)?t.push(239,191,189):t.push(224|r>>12,128|r>>6&63,128|63&r)}return new Uint8Array(t)}function base64Bytes_(e,t){switch(e){case p.BASE64:{const n=-1!==t.indexOf("-"),r=-1!==t.indexOf("_");if(n||r){throw invalidFormat(e,"Invalid character '"+(n?"-":"_")+"' found: is it base64url encoded?")}break}case p.BASE64URL:{const n=-1!==t.indexOf("+"),r=-1!==t.indexOf("/");if(n||r){throw invalidFormat(e,"Invalid character '"+(n?"+":"/")+"' found: is it base64 encoded?")}t=t.replace(/-/g,"+").replace(/_/g,"/");break}}let n;try{n=decodeBase64(t)}catch(t){if(t.message.includes("polyfill"))throw t;throw invalidFormat(e,"Invalid character found")}const r=new Uint8Array(n.length);for(let e=0;e<n.length;e++)r[e]=n.charCodeAt(e);return r}class DataURLParts{constructor(e){this.base64=!1,this.contentType=null;const t=e.match(/^data:([^,]+)?,/);if(null===t)throw invalidFormat(p.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");const n=t[1]||null;null!=n&&(this.base64=function endsWith(e,t){if(!(e.length>=t.length))return!1;return e.substring(e.length-t.length)===t}(n,";base64"),this.contentType=this.base64?n.substring(0,n.length-7):n),this.rest=e.substring(e.indexOf(",")+1)}}class FbsBlob{constructor(e,t){let n=0,r="";isNativeBlob(e)?(this.data_=e,n=e.size,r=e.type):e instanceof ArrayBuffer?(t?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),n=this.data_.length):e instanceof Uint8Array&&(t?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),n=e.length),this.size_=n,this.type_=r}size(){return this.size_}type(){return this.type_}slice(e,t){if(isNativeBlob(this.data_)){const n=function sliceBlob(e,t,n){return e.webkitSlice?e.webkitSlice(t,n):e.mozSlice?e.mozSlice(t,n):e.slice?e.slice(t,n):null}(this.data_,e,t);return null===n?null:new FbsBlob(n)}{const n=new Uint8Array(this.data_.buffer,e,t-e);return new FbsBlob(n,!0)}}static getBlob(...e){if(isNativeBlobDefined()){const t=e.map((e=>e instanceof FbsBlob?e.data_:e));return new FbsBlob(getBlob$1.apply(null,t))}{const t=e.map((e=>isString(e)?dataFromString(p.RAW,e).data:e.data_));let n=0;t.forEach((e=>{n+=e.byteLength}));const r=new Uint8Array(n);let o=0;return t.forEach((e=>{for(let t=0;t<e.length;t++)r[o++]=e[t]})),new FbsBlob(r,!0)}}uploadData(){return this.data_}}function jsonObjectOrNull(e){let t;try{t=JSON.parse(e)}catch(e){return null}return function isNonArrayObject(e){return"object"==typeof e&&!Array.isArray(e)}(t)?t:null}function lastComponent(e){const t=e.lastIndexOf("/",e.length-2);return-1===t?e:e.slice(t+1)}function noXform_(e,t){return t}class Mapping{constructor(e,t,n,r){this.server=e,this.local=t||e,this.writable=!!n,this.xform=r||noXform_}}let _=null;function getMappings(){if(_)return _;const e=[];e.push(new Mapping("bucket")),e.push(new Mapping("generation")),e.push(new Mapping("metageneration")),e.push(new Mapping("name","fullPath",!0));const t=new Mapping("name");t.xform=function mappingsXformPath(e,t){return function xformPath(e){return!isString(e)||e.length<2?e:lastComponent(e)}(t)},e.push(t);const n=new Mapping("size");return n.xform=function xformSize(e,t){return void 0!==t?Number(t):t},e.push(n),e.push(new Mapping("timeCreated")),e.push(new Mapping("updated")),e.push(new Mapping("md5Hash",null,!0)),e.push(new Mapping("cacheControl",null,!0)),e.push(new Mapping("contentDisposition",null,!0)),e.push(new Mapping("contentEncoding",null,!0)),e.push(new Mapping("contentLanguage",null,!0)),e.push(new Mapping("contentType",null,!0)),e.push(new Mapping("metadata","customMetadata",!0)),_=e,_}function fromResource(e,t,n){const r={type:"file"},o=n.length;for(let e=0;e<o;e++){const o=n[e];r[o.local]=o.xform(r,t[o.server])}return function addRef(e,t){Object.defineProperty(e,"ref",{get:function generateRef(){const n=e.bucket,r=e.fullPath,o=new Location(n,r);return t._makeStorageReference(o)}})}(r,e),r}function fromResourceString(e,t,n){const r=jsonObjectOrNull(t);if(null===r)return null;return fromResource(e,r,n)}function toResourceString(e,t){const n={},r=t.length;for(let o=0;o<r;o++){const r=t[o];r.writable&&(n[r.server]=e[r.local])}return JSON.stringify(n)}const f="prefixes",g="items";function fromResponseString(e,t,n){const r=jsonObjectOrNull(n);if(null===r)return null;return function fromBackendResponse(e,t,n){const r={prefixes:[],items:[],nextPageToken:n.nextPageToken};if(n[f])for(const o of n[f]){const n=o.replace(/\/$/,""),s=e._makeStorageReference(new Location(t,n));r.prefixes.push(s)}if(n[g])for(const o of n[g]){const n=e._makeStorageReference(new Location(t,o.name));r.items.push(n)}return r}(e,t,r)}class RequestInfo{constructor(e,t,n,r){this.url=e,this.method=t,this.handler=n,this.timeout=r,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}}function handlerCheck(e){if(!e)throw unknown()}function metadataHandler(e,t){return function handler(n,r){const o=fromResourceString(e,r,t);return handlerCheck(null!==o),o}}function downloadUrlHandler(e,t){return function handler(n,r){const o=fromResourceString(e,r,t);return handlerCheck(null!==o),function downloadUrlFromResourceString(e,t,n,r){const o=jsonObjectOrNull(t);if(null===o)return null;if(!isString(o.downloadTokens))return null;const s=o.downloadTokens;if(0===s.length)return null;const i=encodeURIComponent;return s.split(",").map((t=>{const o=e.bucket,s=e.fullPath;return makeUrl("/b/"+i(o)+"/o/"+i(s),n,r)+makeQueryString({alt:"media",token:t})}))[0]}(o,r,e.host,e._protocol)}}function sharedErrorHandler(e){return function errorHandler(t,n){let r;return r=401===t.getStatus()?t.getErrorText().includes("Firebase App Check token is invalid")?function unauthorizedApp(){return new StorageError(h.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project.")}():function unauthenticated(){return new StorageError(h.UNAUTHENTICATED,"User is not authenticated, please authenticate using Firebase Authentication and try again.")}():402===t.getStatus()?function quotaExceeded(e){return new StorageError(h.QUOTA_EXCEEDED,"Quota for bucket '"+e+"' exceeded, please view quota on https://firebase.google.com/pricing/.")}(e.bucket):403===t.getStatus()?function unauthorized(e){return new StorageError(h.UNAUTHORIZED,"User does not have permission to access '"+e+"'.")}(e.path):n,r.status=t.getStatus(),r.serverResponse=n.serverResponse,r}}function objectErrorHandler(e){const t=sharedErrorHandler(e);return function errorHandler(n,r){let o=t(n,r);return 404===n.getStatus()&&(o=function objectNotFound(e){return new StorageError(h.OBJECT_NOT_FOUND,"Object '"+e+"' does not exist.")}(e.path)),o.serverResponse=r.serverResponse,o}}function getMetadata$2(e,t,n){const r=makeUrl(t.fullServerUrl(),e.host,e._protocol),o=e.maxOperationRetryTime,s=new RequestInfo(r,"GET",metadataHandler(e,n),o);return s.errorHandler=objectErrorHandler(t),s}function list$2(e,t,n,r,o){const s={};t.isRoot?s.prefix="":s.prefix=t.path+"/",n&&n.length>0&&(s.delimiter=n),r&&(s.pageToken=r),o&&(s.maxResults=o);const i=makeUrl(t.bucketOnlyServerUrl(),e.host,e._protocol),a=e.maxOperationRetryTime,l=new RequestInfo(i,"GET",function listHandler(e,t){return function handler(n,r){const o=fromResponseString(e,t,r);return handlerCheck(null!==o),o}}(e,t.bucket),a);return l.urlParams=s,l.errorHandler=sharedErrorHandler(t),l}function getBytes$1(e,t,n){const r=makeUrl(t.fullServerUrl(),e.host,e._protocol)+"?alt=media",o=e.maxOperationRetryTime,s=new RequestInfo(r,"GET",((e,t)=>t),o);return s.errorHandler=objectErrorHandler(t),void 0!==n&&(s.headers.Range=`bytes=0-${n}`,s.successCodes=[200,206]),s}function metadataForUpload_(e,t,n){const r=Object.assign({},n);return r.fullPath=e.path,r.size=t.size(),r.contentType||(r.contentType=function determineContentType_(e,t){return e&&e.contentType||t&&t.type()||"application/octet-stream"}(null,t)),r}function multipartUpload(e,t,n,r,o){const s=t.bucketOnlyServerUrl(),i={"X-Goog-Upload-Protocol":"multipart"};const a=function genBoundary(){let e="";for(let t=0;t<2;t++)e+=Math.random().toString().slice(2);return e}();i["Content-Type"]="multipart/related; boundary="+a;const l=metadataForUpload_(t,r,o),c="--"+a+"\r\nContent-Type: application/json; charset=utf-8\r\n\r\n"+toResourceString(l,n)+"\r\n--"+a+"\r\nContent-Type: "+l.contentType+"\r\n\r\n",u="\r\n--"+a+"--",h=FbsBlob.getBlob(c,r,u);if(null===h)throw cannotSliceBlob();const d={name:l.fullPath},p=makeUrl(s,e.host,e._protocol),_=e.maxUploadRetryTime,f=new RequestInfo(p,"POST",metadataHandler(e,n),_);return f.urlParams=d,f.headers=i,f.body=h.uploadData(),f.errorHandler=sharedErrorHandler(t),f}class ResumableUploadStatus{constructor(e,t,n,r){this.current=e,this.total=t,this.finalized=!!n,this.metadata=r||null}}function checkResumeHeader_(e,t){let n=null;try{n=e.getResponseHeader("X-Goog-Upload-Status")}catch(e){handlerCheck(!1)}return handlerCheck(!!n&&-1!==(t||["active"]).indexOf(n)),n}const m=262144;function continueResumableUpload(e,t,n,r,o,s,i,a){const l=new ResumableUploadStatus(0,0);if(i?(l.current=i.current,l.total=i.total):(l.current=0,l.total=r.size()),r.size()!==l.total)throw function serverFileWrongSize(){return new StorageError(h.SERVER_FILE_WRONG_SIZE,"Server recorded incorrect upload file size, please retry the upload.")}();const c=l.total-l.current;let u=c;o>0&&(u=Math.min(u,o));const d=l.current,p=d+u;let _="";_=0===u?"finalize":c===u?"upload, finalize":"upload";const f={"X-Goog-Upload-Command":_,"X-Goog-Upload-Offset":`${l.current}`},g=r.slice(d,p);if(null===g)throw cannotSliceBlob();const m=t.maxUploadRetryTime,b=new RequestInfo(n,"POST",(function handler(e,n){const o=checkResumeHeader_(e,["active","final"]),i=l.current+u,a=r.size();let c;return c="final"===o?metadataHandler(t,s)(e,n):null,new ResumableUploadStatus(i,a,"final"===o,c)}),m);return b.headers=f,b.body=g.uploadData(),b.progressCallback=a||null,b.errorHandler=sharedErrorHandler(e),b}const b={STATE_CHANGED:"state_changed"},E={RUNNING:"running",PAUSED:"paused",SUCCESS:"success",CANCELED:"canceled",ERROR:"error"};function taskStateFromInternalTaskState(e){switch(e){case"running":case"pausing":case"canceling":return E.RUNNING;case"paused":return E.PAUSED;case"success":return E.SUCCESS;case"canceled":return E.CANCELED;default:return E.ERROR}}class Observer{constructor(e,t,n){if(function isFunction(e){return"function"==typeof e}(e)||null!=t||null!=n)this.next=e,this.error=t??void 0,this.complete=n??void 0;else{const t=e;this.next=t.next,this.error=t.error,this.complete=t.complete}}}function async(e){return(...t)=>{Promise.resolve().then((()=>e(...t)))}}class XhrConnection{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=d.NO_ERROR,this.sendPromise_=new Promise((e=>{this.xhr_.addEventListener("abort",(()=>{this.errorCode_=d.ABORT,e()})),this.xhr_.addEventListener("error",(()=>{this.errorCode_=d.NETWORK_ERROR,e()})),this.xhr_.addEventListener("load",(()=>{e()}))}))}send(e,t,n,r,o){if(this.sent_)throw internalError("cannot .send() more than once");if(isCloudWorkstation(e)&&n&&(this.xhr_.withCredentials=!0),this.sent_=!0,this.xhr_.open(t,e,!0),void 0!==o)for(const e in o)o.hasOwnProperty(e)&&this.xhr_.setRequestHeader(e,o[e].toString());return void 0!==r?this.xhr_.send(r):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw internalError("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw internalError("cannot .getStatus() before sending");try{return this.xhr_.status}catch(e){return-1}}getResponse(){if(!this.sent_)throw internalError("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw internalError("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.removeEventListener("progress",e)}}class XhrTextConnection extends XhrConnection{initXhr(){this.xhr_.responseType="text"}}function newTextConnection(){return new XhrTextConnection}class XhrBytesConnection extends XhrConnection{initXhr(){this.xhr_.responseType="arraybuffer"}}function newBytesConnection(){return new XhrBytesConnection}class XhrBlobConnection extends XhrConnection{initXhr(){this.xhr_.responseType="blob"}}function newBlobConnection(){return new XhrBlobConnection}class UploadTask{isExponentialBackoffExpired(){return this.sleepTime>this.maxSleepTime}constructor(e,t,n=null){this._transferred=0,this._needToFetchStatus=!1,this._needToFetchMetadata=!1,this._observers=[],this._error=void 0,this._uploadUrl=void 0,this._request=void 0,this._chunkMultiplier=1,this._resolve=void 0,this._reject=void 0,this._ref=e,this._blob=t,this._metadata=n,this._mappings=getMappings(),this._resumable=this._shouldDoResumable(this._blob),this._state="running",this._errorHandler=e=>{if(this._request=void 0,this._chunkMultiplier=1,e._codeEquals(h.CANCELED))this._needToFetchStatus=!0,this.completeTransitions_();else{const t=this.isExponentialBackoffExpired();if(isRetryStatusCode(e.status,[])){if(!t)return this.sleepTime=Math.max(2*this.sleepTime,1e3),this._needToFetchStatus=!0,void this.completeTransitions_();e=retryLimitExceeded()}this._error=e,this._transition("error")}},this._metadataErrorHandler=e=>{this._request=void 0,e._codeEquals(h.CANCELED)?this.completeTransitions_():(this._error=e,this._transition("error"))},this.sleepTime=0,this.maxSleepTime=this._ref.storage.maxUploadRetryTime,this._promise=new Promise(((e,t)=>{this._resolve=e,this._reject=t,this._start()})),this._promise.then(null,(()=>{}))}_makeProgressCallback(){const e=this._transferred;return t=>this._updateProgress(e+t)}_shouldDoResumable(e){return e.size()>262144}_start(){"running"===this._state&&void 0===this._request&&(this._resumable?void 0===this._uploadUrl?this._createResumable():this._needToFetchStatus?this._fetchStatus():this._needToFetchMetadata?this._fetchMetadata():this.pendingTimeout=setTimeout((()=>{this.pendingTimeout=void 0,this._continueUpload()}),this.sleepTime):this._oneShotUpload())}_resolveToken(e){Promise.all([this._ref.storage._getAuthToken(),this._ref.storage._getAppCheckToken()]).then((([t,n])=>{switch(this._state){case"running":e(t,n);break;case"canceling":this._transition("canceled");break;case"pausing":this._transition("paused")}}))}_createResumable(){this._resolveToken(((e,t)=>{const n=function createResumableUpload(e,t,n,r,o){const s=t.bucketOnlyServerUrl(),i=metadataForUpload_(t,r,o),a={name:i.fullPath},l=makeUrl(s,e.host,e._protocol),c={"X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${r.size()}`,"X-Goog-Upload-Header-Content-Type":i.contentType,"Content-Type":"application/json; charset=utf-8"},u=toResourceString(i,n),h=e.maxUploadRetryTime,d=new RequestInfo(l,"POST",(function handler(e){let t;checkResumeHeader_(e);try{t=e.getResponseHeader("X-Goog-Upload-URL")}catch(e){handlerCheck(!1)}return handlerCheck(isString(t)),t}),h);return d.urlParams=a,d.headers=c,d.body=u,d.errorHandler=sharedErrorHandler(t),d}(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata),r=this._ref.storage._makeRequest(n,newTextConnection,e,t);this._request=r,r.getPromise().then((e=>{this._request=void 0,this._uploadUrl=e,this._needToFetchStatus=!1,this.completeTransitions_()}),this._errorHandler)}))}_fetchStatus(){const e=this._uploadUrl;this._resolveToken(((t,n)=>{const r=function getResumableUploadStatus(e,t,n,r){const o=e.maxUploadRetryTime,s=new RequestInfo(n,"POST",(function handler(e){const t=checkResumeHeader_(e,["active","final"]);let n=null;try{n=e.getResponseHeader("X-Goog-Upload-Size-Received")}catch(e){handlerCheck(!1)}n||handlerCheck(!1);const o=Number(n);return handlerCheck(!isNaN(o)),new ResumableUploadStatus(o,r.size(),"final"===t)}),o);return s.headers={"X-Goog-Upload-Command":"query"},s.errorHandler=sharedErrorHandler(t),s}(this._ref.storage,this._ref._location,e,this._blob),o=this._ref.storage._makeRequest(r,newTextConnection,t,n);this._request=o,o.getPromise().then((e=>{this._request=void 0,this._updateProgress(e.current),this._needToFetchStatus=!1,e.finalized&&(this._needToFetchMetadata=!0),this.completeTransitions_()}),this._errorHandler)}))}_continueUpload(){const e=m*this._chunkMultiplier,t=new ResumableUploadStatus(this._transferred,this._blob.size()),n=this._uploadUrl;this._resolveToken(((r,o)=>{let s;try{s=continueResumableUpload(this._ref._location,this._ref.storage,n,this._blob,e,this._mappings,t,this._makeProgressCallback())}catch(e){return this._error=e,void this._transition("error")}const i=this._ref.storage._makeRequest(s,newTextConnection,r,o,!1);this._request=i,i.getPromise().then((e=>{this._increaseMultiplier(),this._request=void 0,this._updateProgress(e.current),e.finalized?(this._metadata=e.metadata,this._transition("success")):this.completeTransitions_()}),this._errorHandler)}))}_increaseMultiplier(){2*(m*this._chunkMultiplier)<33554432&&(this._chunkMultiplier*=2)}_fetchMetadata(){this._resolveToken(((e,t)=>{const n=getMetadata$2(this._ref.storage,this._ref._location,this._mappings),r=this._ref.storage._makeRequest(n,newTextConnection,e,t);this._request=r,r.getPromise().then((e=>{this._request=void 0,this._metadata=e,this._transition("success")}),this._metadataErrorHandler)}))}_oneShotUpload(){this._resolveToken(((e,t)=>{const n=multipartUpload(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata),r=this._ref.storage._makeRequest(n,newTextConnection,e,t);this._request=r,r.getPromise().then((e=>{this._request=void 0,this._metadata=e,this._updateProgress(this._blob.size()),this._transition("success")}),this._errorHandler)}))}_updateProgress(e){const t=this._transferred;this._transferred=e,this._transferred!==t&&this._notifyObservers()}_transition(e){if(this._state!==e)switch(e){case"canceling":case"pausing":this._state=e,void 0!==this._request?this._request.cancel():this.pendingTimeout&&(clearTimeout(this.pendingTimeout),this.pendingTimeout=void 0,this.completeTransitions_());break;case"running":const t="paused"===this._state;this._state=e,t&&(this._notifyObservers(),this._start());break;case"paused":case"error":case"success":this._state=e,this._notifyObservers();break;case"canceled":this._error=canceled(),this._state=e,this._notifyObservers()}}completeTransitions_(){switch(this._state){case"pausing":this._transition("paused");break;case"canceling":this._transition("canceled");break;case"running":this._start()}}get snapshot(){const e=taskStateFromInternalTaskState(this._state);return{bytesTransferred:this._transferred,totalBytes:this._blob.size(),state:e,metadata:this._metadata,task:this,ref:this._ref}}on(e,t,n,r){const o=new Observer(t||void 0,n||void 0,r||void 0);return this._addObserver(o),()=>{this._removeObserver(o)}}then(e,t){return this._promise.then(e,t)}catch(e){return this.then(null,e)}_addObserver(e){this._observers.push(e),this._notifyObserver(e)}_removeObserver(e){const t=this._observers.indexOf(e);-1!==t&&this._observers.splice(t,1)}_notifyObservers(){this._finishPromise();this._observers.slice().forEach((e=>{this._notifyObserver(e)}))}_finishPromise(){if(void 0!==this._resolve){let e=!0;switch(taskStateFromInternalTaskState(this._state)){case E.SUCCESS:async(this._resolve.bind(null,this.snapshot))();break;case E.CANCELED:case E.ERROR:async(this._reject.bind(null,this._error))();break;default:e=!1}e&&(this._resolve=void 0,this._reject=void 0)}}_notifyObserver(e){switch(taskStateFromInternalTaskState(this._state)){case E.RUNNING:case E.PAUSED:e.next&&async(e.next.bind(e,this.snapshot))();break;case E.SUCCESS:e.complete&&async(e.complete.bind(e))();break;default:e.error&&async(e.error.bind(e,this._error))()}}resume(){const e="paused"===this._state||"pausing"===this._state;return e&&this._transition("running"),e}pause(){const e="running"===this._state;return e&&this._transition("pausing"),e}cancel(){const e="running"===this._state||"pausing"===this._state;return e&&this._transition("canceling"),e}}class Reference{constructor(e,t){this._service=e,this._location=t instanceof Location?t:Location.makeFromUrl(t,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,t){return new Reference(e,t)}get root(){const e=new Location(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return lastComponent(this._location.path)}get storage(){return this._service}get parent(){const e=function parent(e){if(0===e.length)return null;const t=e.lastIndexOf("/");return-1===t?"":e.slice(0,t)}(this._location.path);if(null===e)return null;const t=new Location(this._location.bucket,e);return new Reference(this._service,t)}_throwIfRoot(e){if(""===this._location.path)throw invalidRootOperation(e)}}function uploadBytes$1(e,t,n){e._throwIfRoot("uploadBytes");const r=multipartUpload(e.storage,e._location,getMappings(),new FbsBlob(t,!0),n);return e.storage.makeRequestWithTokens(r,newTextConnection).then((t=>({metadata:t,ref:e})))}function listAll$1(e){const t={prefixes:[],items:[]};return listAllHelper(e,t).then((()=>t))}async function listAllHelper(e,t,n){const r={pageToken:n},o=await list$1(e,r);t.prefixes.push(...o.prefixes),t.items.push(...o.items),null!=o.nextPageToken&&await listAllHelper(e,t,o.nextPageToken)}function list$1(e,t){null!=t&&"number"==typeof t.maxResults&&validateNumber("options.maxResults",1,1e3,t.maxResults);const n=t||{},r=list$2(e.storage,e._location,"/",n.pageToken,n.maxResults);return e.storage.makeRequestWithTokens(r,newTextConnection)}function updateMetadata$1(e,t){e._throwIfRoot("updateMetadata");const n=function updateMetadata$2(e,t,n,r){const o=makeUrl(t.fullServerUrl(),e.host,e._protocol),s=toResourceString(n,r),i=e.maxOperationRetryTime,a=new RequestInfo(o,"PATCH",metadataHandler(e,r),i);return a.headers={"Content-Type":"application/json; charset=utf-8"},a.body=s,a.errorHandler=objectErrorHandler(t),a}(e.storage,e._location,t,getMappings());return e.storage.makeRequestWithTokens(n,newTextConnection)}function getDownloadURL$1(e){e._throwIfRoot("getDownloadURL");const t=function getDownloadUrl(e,t,n){const r=makeUrl(t.fullServerUrl(),e.host,e._protocol),o=e.maxOperationRetryTime,s=new RequestInfo(r,"GET",downloadUrlHandler(e,n),o);return s.errorHandler=objectErrorHandler(t),s}(e.storage,e._location,getMappings());return e.storage.makeRequestWithTokens(t,newTextConnection).then((e=>{if(null===e)throw function noDownloadURL(){return new StorageError(h.NO_DOWNLOAD_URL,"The given file does not have any download URLs.")}();return e}))}function deleteObject$1(e){e._throwIfRoot("deleteObject");const t=function deleteObject$2(e,t){const n=makeUrl(t.fullServerUrl(),e.host,e._protocol),r=e.maxOperationRetryTime,o=new RequestInfo(n,"DELETE",(function handler(e,t){}),r);return o.successCodes=[200,204],o.errorHandler=objectErrorHandler(t),o}(e.storage,e._location);return e.storage.makeRequestWithTokens(t,newTextConnection)}function _getChild$1(e,t){const n=function child(e,t){const n=t.split("/").filter((e=>e.length>0)).join("/");return 0===e.length?n:e+"/"+n}(e._location.path,t),r=new Location(e._location.bucket,n);return new Reference(e.storage,r)}function refFromPath(e,t){if(e instanceof FirebaseStorageImpl){const n=e;if(null==n._bucket)throw function noDefaultBucket(){return new StorageError(h.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+u+"' property when initializing the app?")}();const r=new Reference(n,n._bucket);return null!=t?refFromPath(r,t):r}return void 0!==t?_getChild$1(e,t):e}function ref$1(e,t){if(t&&function isUrl(e){return/^[A-Za-z]+:\/\//.test(e)}(t)){if(e instanceof FirebaseStorageImpl)return function refFromURL(e,t){return new Reference(e,t)}(e,t);throw invalidArgument("To use ref(service, url), the first argument must be a Storage instance.")}return refFromPath(e,t)}function extractBucket(e,t){const n=t?.[u];return null==n?null:Location.makeFromBucketSpec(n,e)}function connectStorageEmulator$1(e,t,n,r={}){e.host=`${t}:${n}`;const o=isCloudWorkstation(t);o&&(!async function pingServer(e){return(await fetch(e,{credentials:"include"})).ok}(`https://${e.host}/b`),updateEmulatorBanner("Storage",!0)),e._isUsingEmulator=!0,e._protocol=o?"https":"http";const{mockUserToken:s}=r;s&&(e._overrideAuthToken="string"==typeof s?s:function createMockUserToken(e,t){if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');const n=t||"demo-project",r=e.iat||0,o=e.sub||e.user_id;if(!o)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");const s={iss:`https://securetoken.google.com/${n}`,aud:n,iat:r,exp:r+3600,auth_time:r,sub:o,user_id:o,firebase:{sign_in_provider:"custom",identities:{}},...e};return[base64urlEncodeWithoutPadding(JSON.stringify({alg:"none",type:"JWT"})),base64urlEncodeWithoutPadding(JSON.stringify(s)),""].join(".")}(s,e.app.options.projectId))}class FirebaseStorageImpl{constructor(e,t,n,r,o,s=!1){this.app=e,this._authProvider=t,this._appCheckProvider=n,this._url=r,this._firebaseVersion=o,this._isUsingEmulator=s,this._bucket=null,this._host=c,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,this._bucket=null!=r?Location.makeFromBucketSpec(r,this._host):extractBucket(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,null!=this._url?this._bucket=Location.makeFromBucketSpec(this._url,e):this._bucket=extractBucket(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){validateNumber("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){validateNumber("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;const e=this._authProvider.getImmediate({optional:!0});if(e){const t=await e.getToken();if(null!==t)return t.accessToken}return null}async _getAppCheckToken(){if(r(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;const e=this._appCheckProvider.getImmediate({optional:!0});if(e){return(await e.getToken()).token}return null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach((e=>e.cancel())),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new Reference(this,e)}_makeRequest(e,t,n,r,o=!0){if(this._deleted)return new FailRequest(appDeleted());{const s=function makeRequest(e,t,n,r,o,s,i=!0,a=!1){const l=makeQueryString(e.urlParams),c=e.url+l,u=Object.assign({},e.headers);return function addGmpidHeader_(e,t){t&&(e["X-Firebase-GMPID"]=t)}(u,t),function addAuthHeader_(e,t){null!==t&&t.length>0&&(e.Authorization="Firebase "+t)}(u,n),function addVersionHeader_(e,t){e["X-Firebase-Storage-Version"]="webjs/"+(t??"AppManager")}(u,s),function addAppCheckHeader_(e,t){null!==t&&(e["X-Firebase-AppCheck"]=t)}(u,r),new NetworkRequest(c,e.method,u,e.body,e.successCodes,e.additionalRetryCodes,e.handler,e.errorHandler,e.timeout,e.progressCallback,o,i,a)}(e,this._appId,n,r,t,this._firebaseVersion,o,this._isUsingEmulator);return this._requests.add(s),s.getPromise().then((()=>this._requests.delete(s)),(()=>this._requests.delete(s))),s}}async makeRequestWithTokens(e,t){const[n,r]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,t,n,r).getPromise()}}const y="@firebase/storage",T="0.14.0",R="storage";function getBytes(e,t){return function getBytesInternal(e,t){e._throwIfRoot("getBytes");const n=getBytes$1(e.storage,e._location,t);return e.storage.makeRequestWithTokens(n,newBytesConnection).then((e=>void 0!==t?e.slice(0,t):e))}(e=getModularInstance(e),t)}function uploadBytes(e,t,n){return uploadBytes$1(e=getModularInstance(e),t,n)}function uploadString(e,t,n,r){return function uploadString$1(e,t,n=p.RAW,r){e._throwIfRoot("uploadString");const o=dataFromString(n,t),s={...r};return null==s.contentType&&null!=o.contentType&&(s.contentType=o.contentType),uploadBytes$1(e,o.data,s)}(e=getModularInstance(e),t,n,r)}function uploadBytesResumable(e,t,n){return function uploadBytesResumable$1(e,t,n){return e._throwIfRoot("uploadBytesResumable"),new UploadTask(e,new FbsBlob(t),n)}(e=getModularInstance(e),t,n)}function getMetadata(e){return function getMetadata$1(e){e._throwIfRoot("getMetadata");const t=getMetadata$2(e.storage,e._location,getMappings());return e.storage.makeRequestWithTokens(t,newTextConnection)}(e=getModularInstance(e))}function updateMetadata(e,t){return updateMetadata$1(e=getModularInstance(e),t)}function list(e,t){return list$1(e=getModularInstance(e),t)}function listAll(e){return listAll$1(e=getModularInstance(e))}function getDownloadURL(e){return getDownloadURL$1(e=getModularInstance(e))}function deleteObject(e){return deleteObject$1(e=getModularInstance(e))}function ref(e,t){return ref$1(e=getModularInstance(e),t)}function _getChild(e,t){return _getChild$1(e,t)}function getStorage(t=e(),n){t=getModularInstance(t);const r=_getProvider(t,R).getImmediate({identifier:n}),o=getDefaultEmulatorHostnameAndPort("storage");return o&&connectStorageEmulator(r,...o),r}function connectStorageEmulator(e,t,n,r={}){connectStorageEmulator$1(e,t,n,r)}function getBlob(e,t){return function getBlobInternal(e,t){e._throwIfRoot("getBlob");const n=getBytes$1(e.storage,e._location,t);return e.storage.makeRequestWithTokens(n,newBlobConnection).then((e=>void 0!==t?e.slice(0,t):e))}(e=getModularInstance(e),t)}function getStream(e,t){throw new Error("getStream() is only supported by NodeJS builds")}function factory(e,{instanceIdentifier:t}){const n=e.getProvider("app").getImmediate(),r=e.getProvider("auth-internal"),s=e.getProvider("app-check-internal");return new FirebaseStorageImpl(n,r,s,t,o)}!function registerStorage(){t(new Component(R,factory,"PUBLIC").setMultipleInstances(!0)),n(y,T,""),n(y,T,"esm2020")}();export{StorageError,h as StorageErrorCode,p as StringFormat,FbsBlob as _FbsBlob,Location as _Location,b as _TaskEvent,E as _TaskState,UploadTask as _UploadTask,dataFromString as _dataFromString,_getChild,invalidArgument as _invalidArgument,invalidRootOperation as _invalidRootOperation,connectStorageEmulator,deleteObject,getBlob,getBytes,getDownloadURL,getMetadata,getStorage,getStream,list,listAll,ref,updateMetadata,uploadBytes,uploadBytesResumable,uploadString};

//# sourceMappingURL=firebase-storage.js.map
