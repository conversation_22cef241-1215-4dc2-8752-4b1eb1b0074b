// Test Firestore read
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../../config/firebase';

const testFirestore = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, 'restaurants'));
    querySnapshot.forEach((doc) => {
      console.log(doc.id, ' => ', doc.data());
    });
  } catch (error) {
    console.error('Firestore test failed:', error);
  }
};

// Call the testFirestore function to use it
testFirestore();