((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Lr,Nr){try{!(function(){function O(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t,F=O(Lr);class x extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,x.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,U.prototype.create)}}class U{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},a=this.service+"/"+e,i=this.errors[e],i=i?(n=r,i.replace(j,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",i=this.serviceName+`: ${i} (${a}).`;return new x(a,i,r)}}let j=/\{\$([^}]+)}/g;class e{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}(I=t=t||{})[I.DEBUG=0]="DEBUG",I[I.VERBOSE=1]="VERBOSE",I[I.INFO=2]="INFO",I[I.WARN=3]="WARN",I[I.ERROR=4]="ERROR",I[I.SILENT=5]="SILENT";let q={debug:t.DEBUG,verbose:t.VERBOSE,info:t.INFO,warn:t.WARN,error:t.ERROR,silent:t.SILENT},H=t.INFO,V={[t.DEBUG]:"log",[t.VERBOSE]:"log",[t.INFO]:"info",[t.WARN]:"warn",[t.ERROR]:"error"},$=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),a=V[t];if(!a)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[a](`[${n}]  ${e.name}:`,...r)}};function W(e){if("loading"===document.readyState)return"loading";var t=u();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"}function z(e,t){var r="";try{for(;e&&9!==e.nodeType;){var n=e,a=n.id?"#"+n.id:re(n)+(n.classList&&n.classList.value&&n.classList.value.trim()&&n.classList.value.trim().length?"."+n.classList.value.trim().replace(/\s+/g,"."):"");if(r.length+a.length>(t||100)-1)return r||a;if(r=r?a+">"+r:a,n.id)break;e=n.parentNode}}catch(e){}return r}function K(a,e){var o,s,i,c;o=function(e){r={},(e=e).entries.length&&(t=e.entries.reduce(function(e,t){return e&&e.value>t.value?e:t}))&&t.sources&&t.sources.length&&(n=(n=t.sources).find(function(e){return e.node&&1===e.node.nodeType})||n[0])&&(r={largestShiftTarget:z(n.node),largestShiftTime:t.startTime,largestShiftValue:t.value,largestShiftSource:n,largestShiftEntry:t,loadState:W(t.startTime)});var t,r,n=Object.assign(e,{attribution:r});a(n)},s=e||{},i=se(function(){function e(e){e.forEach(function(e){var t,r;e.hadRecentInput||(t=a[0],r=a[a.length-1],n&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(n+=e.value,a.push(e)):(n=e.value,a=[e]))}),n>r.value&&(r.value=n,r.entries=a,t())}var t,r=p("CLS",0),n=0,a=[],i=f("layout-shift",e);i&&(t=g(o,r,ge,s.reportAllChanges),m(function(){e(i.takeRecords()),t(!0)}),d(function(){r=p("CLS",n=0),t=g(o,r,ge,s.reportAllChanges),oe(function(){return t()})}),setTimeout(t,0))}),c=c||{},pe(function(){var t,r=de(),n=p("FCP"),a=f("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(a.disconnect(),e.startTime<r.firstHiddenTime)&&(n.value=Math.max(e.startTime-ie(),0),n.entries.push(e),t(!0))})});a&&(t=g(i,n,fe,c.reportAllChanges),d(function(e){n=p("FCP"),t=g(i,n,fe,c.reportAllChanges),oe(function(){n.value=performance.now()-e.timeStamp,t(!0)})}))})}function G(i,o){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(o=o||{},pe(function(){var e;ye();function t(t){Se(function(){t.forEach(_e);var e=Ee();e&&e.latency!==r.value&&(r.value=e.latency,r.entries=e.entries,a())})}var r=p("INP"),n=f("event",t,{durationThreshold:null!=(e=o.durationThreshold)?e:40}),a=g(i,r,Ie,o.reportAllChanges);n&&(n.observe({type:"first-input",buffered:!0}),m(function(){t(n.takeRecords()),a(!0)}),d(function(){we=be(),s.length=0,c.clear(),r=p("INP"),a=g(i,r,Ie,o.reportAllChanges)}))}))}function J(e){o=o.concat(e),Le()}function Z(){10<h.size&&h.forEach(function(e,t){c.has(t)||h.delete(t)});var r=s.map(function(e){return Me.get(e.entries[0])}),n=l.length-50;l=l.filter(function(e,t){return n<=t||r.includes(e)});for(var a=new Set,e=0;e<l.length;e++){var t=l[e];Ne(t.startTime,t.processingEnd).forEach(function(e){a.add(e)})}var i=o.length-1-50;o=o.filter(function(e,t){return e.startTime>Ce&&i<t||a.has(e)}),ke=-1}function X(c,e){ee=ee||f("long-animation-frame",J),G(function(e){t=(e=e).entries[0],s=Me.get(t),r=t.processingStart,n=s.processingEnd,s=s.entries.sort(function(e,t){return e.processingStart-t.processingStart}),a=Ne(t.startTime,n),i=(i=e.entries.find(function(e){return e.target}))&&i.target||h.get(t.interactionId),o=[t.startTime+t.duration,n].concat(a.map(function(e){return e.startTime+e.duration})),o=Math.max.apply(Math,o),i={interactionTarget:z(i),interactionTargetElement:i,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:o,processedEventEntries:s,longAnimationFrameEntries:a,inputDelay:r-t.startTime,processingDuration:n-r,presentationDelay:Math.max(o-n,0),loadState:W(t.startTime)};var t,r,n,a,i,o,s=Object.assign(e,{attribution:i});c(s)},e)}function Y(l,e){var o,s;o=function(e){s={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:(e=e).value},e.entries.length&&(t=u())&&(o=t.activationStart||0,n=(r=e.entries[e.entries.length-1]).url&&performance.getEntriesByType("resource").filter(function(e){return e.name===r.url})[0],c=Math.max(0,t.responseStart-o),a=Math.max(c,n?(n.requestStart||n.startTime)-o:0),i=Math.max(a,n?n.responseEnd-o:0),o=Math.max(i,r.startTime-o),s={element:z(r.element),timeToFirstByte:c,resourceLoadDelay:a-c,resourceLoadDuration:i-a,elementRenderDelay:o-i,navigationEntry:t,lcpEntry:r},r.url&&(s.url=r.url),n)&&(s.lcpResourceEntry=n);var t,r,n,a,i,o,s,c=Object.assign(e,{attribution:s});l(c)},s=e||{},pe(function(){function e(e){(e=s.reportAllChanges?e:e.slice(-1)).forEach(function(e){e.startTime<n.firstHiddenTime&&(a.value=Math.max(e.startTime-ie(),0),a.entries=[e],t())})}var t,r,n=de(),a=p("LCP"),i=f("largest-contentful-paint",e);i&&(t=g(o,a,Ae,s.reportAllChanges),r=se(function(){Pe[a.id]||(e(i.takeRecords()),i.disconnect(),Pe[a.id]=!0,t(!0))}),["keydown","click"].forEach(function(e){addEventListener(e,function(){return Se(r)},{once:!0,capture:!0})}),m(r),d(function(e){a=p("LCP"),t=g(o,a,Ae,s.reportAllChanges),oe(function(){a.value=performance.now()-e.timeStamp,Pe[a.id]=!0,t(!0)})}))})}var Q,ee,te,u=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&0<e.responseStart&&e.responseStart<performance.now())return e},re=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},ne=-1,ae=function(){return ne},d=function(t){addEventListener("pageshow",function(e){e.persisted&&(ne=e.timeStamp,t(e))},!0)},ie=function(){var e=u();return e&&e.activationStart||0},p=function(e,t){var r=u(),n="navigate";return 0<=ae()?n="back-forward-cache":r&&(document.prerendering||0<ie()?n="prerender":document.wasDiscarded?n="restore":r.type&&(n=r.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},f=function(e,t,r){try{var n;if(PerformanceObserver.supportedEntryTypes.includes(e))return(n=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})})).observe(Object.assign({type:e,buffered:!0},r||{})),n}catch(e){}},g=function(t,r,n,a){var i,o;return function(e){0<=r.value&&(e||a)&&((o=r.value-(i||0))||void 0===i)&&(i=r.value,r.delta=o,r.rating=(e=r.value)>n[1]?"poor":e>n[0]?"needs-improvement":"good",t(r))}},oe=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},m=function(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})},se=function(e){var t=!1;return function(){t||(e(),t=!0)}},r=-1,ce=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},n=function(e){"hidden"===document.visibilityState&&-1<r&&(r="visibilitychange"===e.type?e.timeStamp:0,ue())},le=function(){addEventListener("visibilitychange",n,!0),addEventListener("prerenderingchange",n,!0)},ue=function(){removeEventListener("visibilitychange",n,!0),removeEventListener("prerenderingchange",n,!0)},de=function(){return r<0&&(r=ce(),le(),d(function(){setTimeout(function(){r=ce(),le()},0)})),{get firstHiddenTime(){return r}}},pe=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},fe=[1800,3e3],ge=[.1,.25],me=0,he=1/0,a=0,ve=function(e){e.forEach(function(e){e.interactionId&&(he=Math.min(he,e.interactionId),a=Math.max(a,e.interactionId),me=a?(a-he)/7+1:0)})},be=function(){return Q?me:performance.interactionCount||0},ye=function(){"interactionCount"in performance||(Q=Q||f("event",ve,{type:"event",buffered:!0,durationThreshold:0}))},s=[],c=new Map,we=0,Ee=function(){var e=Math.min(s.length-1,Math.floor((be()-we)/50));return s[e]},Te=[],_e=function(t){var e,r;Te.forEach(function(e){return e(t)}),!t.interactionId&&"first-input"!==t.entryType||(r=s[s.length-1],((e=c.get(t.interactionId))||s.length<10||t.duration>r.latency)&&(e?t.duration>e.latency?(e.entries=[t],e.latency=t.duration):t.duration===e.latency&&t.startTime===e.entries[0].startTime&&e.entries.push(t):(r={id:t.interactionId,latency:t.duration,entries:[t]},c.set(r.id,r),s.push(r)),s.sort(function(e,t){return t.latency-e.latency}),10<s.length)&&s.splice(10).forEach(function(e){return c.delete(e.id)}))},Se=function(e){var t=self.requestIdleCallback||self.setTimeout,r=-1;return e=se(e),"hidden"===document.visibilityState?e():(r=t(e),m(e)),r},Ie=[200,500],o=[],l=[],Ce=0,Me=new WeakMap,h=new Map,ke=-1,Le=function(){ke<0&&(ke=Se(Z))},Ne=(Te.push(function(e){e.interactionId&&e.target&&!h.has(e.interactionId)&&h.set(e.interactionId,e.target)},function(e){var t,r=e.startTime+e.duration;Ce=Math.max(Ce,e.processingEnd);for(var n=l.length-1;0<=n;n--){var a=l[n];if(Math.abs(r-a.renderTime)<=8){(t=a).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:r,entries:[e]},l.push(t)),!e.interactionId&&"first-input"!==e.entryType||Me.set(e,t),Le()}),function(e,t){for(var r,n=[],a=0;r=o[a];a++)if(!(r.startTime+r.duration<e)){if(r.startTime>t)break;n.push(r)}return n}),Ae=[2500,4e3],Pe={};let Re=(t,e)=>e.some(e=>t instanceof e),De,Be;let Oe=new WeakMap,Fe=new WeakMap,xe=new WeakMap,Ue=new WeakMap,je=new WeakMap;let qe={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return Fe.get(e);if("objectStoreNames"===t)return e.objectStoreNames||xe.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return v(e[t])},set(e,t,r){return e[t]=r,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function He(n){return n!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(Be=Be||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(n)?function(...e){return n.apply($e(this),e),v(Oe.get(this))}:function(...e){return v(n.apply($e(this),e))}:function(e,...t){var r=n.call($e(this),e,...t);return xe.set(r,e.sort?e.sort():[e]),v(r)}}function Ve(e){var i,t;return"function"==typeof e?He(e):(e instanceof IDBTransaction&&(i=e,Fe.has(i)||(t=new Promise((e,t)=>{let r=()=>{i.removeEventListener("complete",n),i.removeEventListener("error",a),i.removeEventListener("abort",a)},n=()=>{e(),r()},a=()=>{t(i.error||new DOMException("AbortError","AbortError")),r()};i.addEventListener("complete",n),i.addEventListener("error",a),i.addEventListener("abort",a)}),Fe.set(i,t))),Re(e,De=De||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,qe):e)}function v(e){var i,t;return e instanceof IDBRequest?(i=e,(t=new Promise((e,t)=>{let r=()=>{i.removeEventListener("success",n),i.removeEventListener("error",a)},n=()=>{e(v(i.result)),r()},a=()=>{t(i.error),r()};i.addEventListener("success",n),i.addEventListener("error",a)})).then(e=>{e instanceof IDBCursor&&Oe.set(e,i)}).catch(()=>{}),je.set(t,i),t):Ue.has(e)?Ue.get(e):((t=Ve(e))!==e&&(Ue.set(e,t),je.set(t,e)),t)}let $e=e=>je.get(e);let We=["get","getKey","getAll","getAllKeys","count"],ze=["put","add","delete","clear"],Ke=new Map;function Ge(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(Ke.get(t))return Ke.get(t);let a=t.replace(/FromIndex$/,""),i=t!==a,o=ze.includes(a);var r;return a in(i?IDBIndex:IDBObjectStore).prototype&&(o||We.includes(a))?(r=async function(e,...t){var r=this.transaction(e,o?"readwrite":"readonly");let n=r.store;return i&&(n=n.index(t.shift())),(await Promise.all([n[a](...t),o&&r.done]))[0]},Ke.set(t,r),r):void 0}}qe={...te=qe,get:(e,t,r)=>Ge(e,t)||te.get(e,t,r),has:(e,t)=>!!Ge(e,t)||te.has(e,t)};var i="@firebase/installations",Je="0.6.19";let Ze=1e4,Xe="w:"+Je,Ye="FIS_v2",Qe="https://firebaseinstallations.googleapis.com/v1",et=36e5;let b=new U("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function tt(e){return e instanceof x&&e.code.includes("request-failed")}function rt({projectId:e}){return Qe+`/projects/${e}/installations`}function nt(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function at(e,t){var r=(await t.json()).error;return b.create("request-failed",{requestName:e,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function it({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function ot(e,{refreshToken:t}){var r=it(e);return r.append("Authorization",(e=t,Ye+" "+e)),r}async function st(e){var t=await e();return 500<=t.status&&t.status<600?e():t}function ct(t){return new Promise(e=>{setTimeout(e,t)})}let lt=/^[cdef][\w-]{21}$/,ut="";function dt(){try{var e=new Uint8Array(17),t=((self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16,(e=>btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_"))(e).substr(0,22));return lt.test(t)?t:ut}catch{return ut}}function y(e){return e.appName+"!"+e.appId}let pt=new Map;function ft(e,t){var r=y(e),e=(gt(r,t),r),r=(()=>(!w&&"BroadcastChannel"in self&&((w=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{gt(e.data.key,e.data.fid)}),w))();r&&r.postMessage({key:e,fid:t}),0===pt.size&&w&&(w.close(),w=null)}function gt(e,t){var r=pt.get(e);if(r)for(var n of r)n(t)}let w=null;let mt="firebase-installations-database",ht=1,E="firebase-installations-store",vt=null;function bt(){return vt=vt||((e,t,{blocked:r,upgrade:n,blocking:a,terminated:i})=>{let o=indexedDB.open(e,t);var s=v(o);return n&&o.addEventListener("upgradeneeded",e=>{n(v(o.result),e.oldVersion,e.newVersion,v(o.transaction),e)}),r&&o.addEventListener("blocked",e=>r(e.oldVersion,e.newVersion,e)),s.then(e=>{i&&e.addEventListener("close",()=>i()),a&&e.addEventListener("versionchange",e=>a(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s})(mt,ht,{upgrade:(e,t)=>{0===t&&e.createObjectStore(E)}})}async function T(e,t){var r=y(e),n=(await bt()).transaction(E,"readwrite"),a=n.objectStore(E),i=await a.get(r);return await a.put(t,r),await n.done,i&&i.fid===t.fid||ft(e,t.fid),t}async function yt(e){var t=y(e),r=(await bt()).transaction(E,"readwrite");await r.objectStore(E).delete(t),await r.done}async function _(e,t){var r=y(e),n=(await bt()).transaction(E,"readwrite"),a=n.objectStore(E),i=await a.get(r),o=t(i);return void 0===o?await a.delete(r):await a.put(o,r),await n.done,!o||i&&i.fid===o.fid||ft(e,o.fid),o}async function wt(r){let n;var e=await _(r.appConfig,e=>{var t=Tt(e||{fid:dt(),registrationStatus:0}),t=((e,t)=>{var r,n;return 0===t.registrationStatus?navigator.onLine?(r={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},n=(async(t,r)=>{try{var e=await(async({appConfig:e,heartbeatServiceProvider:t},{fid:r})=>{let n=rt(e);var a=it(e),i=((i=t.getImmediate({optional:!0}))&&(i=await i.getHeartbeatsHeader())&&a.append("x-firebase-client",i),{fid:r,authVersion:Ye,appId:e.appId,sdkVersion:Xe});let o={method:"POST",headers:a,body:JSON.stringify(i)};if((a=await st(()=>fetch(n,o))).ok)return{fid:(i=await a.json()).fid||r,registrationStatus:2,refreshToken:i.refreshToken,authToken:nt(i.authToken)};throw await at("Create Installation",a)})(t,r);return T(t.appConfig,e)}catch(e){throw tt(e)&&409===e.customData.serverCode?await yt(t.appConfig):await T(t.appConfig,{fid:r.fid,registrationStatus:0}),e}})(e,r),{installationEntry:r,registrationPromise:n}):(r=Promise.reject(b.create("app-offline")),{installationEntry:t,registrationPromise:r}):1===t.registrationStatus?{installationEntry:t,registrationPromise:(async e=>{let t=await Et(e.appConfig);for(;1===t.registrationStatus;)await ct(100),t=await Et(e.appConfig);var r,n;return 0!==t.registrationStatus?t:({installationEntry:r,registrationPromise:n}=await wt(e),n||r)})(e)}:{installationEntry:t}})(r,t);return n=t.registrationPromise,t.installationEntry});return e.fid===ut?{installationEntry:await n}:{installationEntry:e,registrationPromise:n}}function Et(e){return _(e,e=>{if(e)return Tt(e);throw b.create("installation-not-found")})}function Tt(e){var t;return 1===(t=e).registrationStatus&&t.registrationTime+Ze<Date.now()?{fid:e.fid,registrationStatus:0}:e}async function _t({appConfig:e,heartbeatServiceProvider:t},r){[a,i]=[e,r.fid];let n=rt(a)+`/${i}/authTokens:generate`;var a,i,o=ot(e,r),s=t.getImmediate({optional:!0}),s=(s&&(s=await s.getHeartbeatsHeader())&&o.append("x-firebase-client",s),{installation:{sdkVersion:Xe,appId:e.appId}});let c={method:"POST",headers:o,body:JSON.stringify(s)};o=await st(()=>fetch(n,c));if(o.ok)return nt(await o.json());throw await at("Generate Auth Token",o)}async function St(n,a=!1){let i;var e=await _(n.appConfig,e=>{if(!Ct(e))throw b.create("not-registered");var t,r=e.authToken;if(a||2!==(t=r).requestStatus||(e=>{var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+et})(t)){if(1===r.requestStatus)return i=(async(e,t)=>{let r=await It(e.appConfig);for(;1===r.authToken.requestStatus;)await ct(100),r=await It(e.appConfig);var n=r.authToken;return 0===n.requestStatus?St(e,t):n})(n,a),e;if(navigator.onLine)return t=e,r={requestStatus:1,requestTime:Date.now()},r={...t,authToken:r},i=(async(t,r)=>{try{var e=await _t(t,r),n={...r,authToken:e};return await T(t.appConfig,n),e}catch(e){var a;throw!tt(e)||401!==e.customData.serverCode&&404!==e.customData.serverCode?(a={...r,authToken:{requestStatus:0}},await T(t.appConfig,a)):await yt(t.appConfig),e}})(n,r),r;throw b.create("app-offline")}return e});return i?await i:e.authToken}function It(e){return _(e,e=>{var t,r;if(Ct(e))return t=e.authToken,1===(r=t).requestStatus&&r.requestTime+Ze<Date.now()?{...e,authToken:{requestStatus:0}}:e;throw b.create("not-registered")})}function Ct(e){return void 0!==e&&2===e.registrationStatus}async function Mt(e,t=!1){var r=e,n=(await(!(n=(await wt(r)).registrationPromise)||!await n),await St(r,t));return n.token}function kt(e){return b.create("missing-app-config-values",{valueName:e})}let Lt="installations",Nt=e=>{var t=e.getProvider("app").getImmediate();return{app:t,appConfig:(e=>{if(!e||!e.options)throw kt("App Configuration");if(!e.name)throw kt("App Name");var t;for(t of["projectId","apiKey","appId"])if(!e.options[t])throw kt(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}})(t),heartbeatServiceProvider:Nr._getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},At=e=>{var t=e.getProvider("app").getImmediate();let r=Nr._getProvider(t,Lt).getImmediate();return{getId:()=>(async e=>{var t=e,{installationEntry:r,registrationPromise:n}=await wt(t);return(n||St(t)).catch(console.error),r.fid})(r),getToken:e=>Mt(r,e)}};Nr._registerComponent(new e(Lt,Nt,"PUBLIC")),Nr._registerComponent(new e("installations-internal",At,"PRIVATE")),Nr.registerVersion(i,Je),Nr.registerVersion(i,Je,"esm2020");let Pt="@firebase/performance",Rt="0.7.8",Dt=Rt,Bt="FB-PERF-TRACE-MEASURE",Ot="@firebase/performance/config",Ft="@firebase/performance/configexpire";var S,I,i="Performance";let C=new U("performance",i,{"trace started":"Trace {$traceName} was started before.","trace stopped":"Trace {$traceName} is not running.","nonpositive trace startTime":"Trace {$traceName} startTime should be positive.","nonpositive trace duration":"Trace {$traceName} duration should be positive.","no window":"Window is not available.","no app id":"App id is not available.","no project id":"Project id is not available.","no api key":"Api key is not available.","invalid cc log":"Attempted to queue invalid cc event","FB not default":"Performance can only start when Firebase app instance is the default one.","RC response not ok":"RC response is not ok","invalid attribute name":"Attribute name {$attributeName} is invalid.","invalid attribute value":"Attribute value {$attributeValue} is invalid.","invalid custom metric name":"Custom metric name {$customMetricName} is invalid","invalid String merger input":"Input for String merger is invalid, contact support team to resolve.","already initialized":"initializePerformance() has already been called with different options. To avoid this error, call initializePerformance() with the same options as when it was originally called, or call getPerformance() to return the already initialized instance."}),M=new class{constructor(e){this.name=e,this._logLevel=H,this._logHandler=$,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in t))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?q[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,t.DEBUG,...e),this._logHandler(this,t.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,t.VERBOSE,...e),this._logHandler(this,t.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,t.INFO,...e),this._logHandler(this,t.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,t.WARN,...e),this._logHandler(this,t.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,t.ERROR,...e),this._logHandler(this,t.ERROR,...e)}}(i);M.logLevel=t.INFO;let xt,Ut;class k{constructor(e){if(!(this.window=e))throw C.create("no window");this.performance=e.performance,this.PerformanceObserver=e.PerformanceObserver,this.windowLocation=e.location,this.navigator=e.navigator,this.document=e.document,this.navigator&&this.navigator.cookieEnabled&&(this.localStorage=e.localStorage),e.perfMetrics&&e.perfMetrics.onFirstInputDelay&&(this.onFirstInputDelay=e.perfMetrics.onFirstInputDelay),this.onLCP=Y,this.onINP=X,this.onCLS=K}getUrl(){return this.windowLocation.href.split("?")[0]}mark(e){this.performance&&this.performance.mark&&this.performance.mark(e)}measure(e,t,r){this.performance&&this.performance.measure&&this.performance.measure(e,t,r)}getEntriesByType(e){return this.performance&&this.performance.getEntriesByType?this.performance.getEntriesByType(e):[]}getEntriesByName(e){return this.performance&&this.performance.getEntriesByName?this.performance.getEntriesByName(e):[]}getTimeOrigin(){return this.performance&&(this.performance.timeOrigin||this.performance.timing.navigationStart)}requiredApisAvailable(){return fetch&&Promise&&"undefined"!=typeof navigator&&navigator.cookieEnabled?!!(()=>{try{return"object"==typeof indexedDB}catch(e){}})()||(M.info("IndexedDB is not supported by current browser"),!1):(M.info("Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled."),!1)}setupObserver(e,r){this.PerformanceObserver&&new this.PerformanceObserver(e=>{for(var t of e.getEntries())r(t)}).observe({entryTypes:[e]})}static getInstance(){return xt=void 0===xt?new k(Ut):xt}}let L;function jt(e,t){var r=e.length-t.length;if(r<0||1<r)throw C.create("invalid String merger input");var n=[];for(let a=0;a<e.length;a++)n.push(e.charAt(a)),t.length>a&&n.push(t.charAt(a));return n.join("")}let qt;class N{constructor(){this.instrumentationEnabled=!0,this.dataCollectionEnabled=!0,this.loggingEnabled=!1,this.tracesSamplingRate=1,this.networkRequestsSamplingRate=1,this.logEndPointUrl="https://firebaselogging.googleapis.com/v0cc/log?format=json_proto",this.flTransportEndpointUrl=jt("hts/frbslgigp.ogepscmv/ieo/eaylg","tp:/ieaeogn-agolai.o/1frlglgc/o"),this.transportKey=jt("AzSC8r6ReiGqFMyfvgow","Iayx0u-XT3vksVM-pIV"),this.logSource=462,this.logTraceAfterSampling=!1,this.logNetworkAfterSampling=!1,this.configTimeToLive=12,this.logMaxFlushSize=40}getFlTransportFullUrl(){return this.flTransportEndpointUrl.concat("?key=",this.transportKey)}static getInstance(){return qt=void 0===qt?new N:qt}}(I=S=S||{})[I.UNKNOWN=0]="UNKNOWN",I[I.VISIBLE=1]="VISIBLE",I[I.HIDDEN=2]="HIDDEN";let Ht=["firebase_","google_","ga_"],Vt=new RegExp("^[a-zA-Z]\\w*$");function $t(e){var t=e.options?.appId;if(t)return t;throw C.create("no app id")}let Wt="0.0.1",A={loggingEnabled:!0},zt="FIREBASE_INSTALLATIONS_AUTH";function Kt(e,t){var n,a,r=(()=>{var e=k.getInstance().localStorage;if(e){var t=e.getItem(Ft);if(t&&(e=>Number(e)>Date.now())(t)){t=e.getItem(Ot);if(t)try{return JSON.parse(t)}catch{}}}})();return r?(Jt(r),Promise.resolve()):(a=t,(e=>{var t=e.getToken();return t.then(e=>{}),t})((n=e).installations).then(e=>{var t=(e=>{var t=e.options?.projectId;if(t)return t;throw C.create("no project id")})(n.app),r=(e=>{var t=e.options?.apiKey;if(t)return t;throw C.create("no api key")})(n.app),t=new Request(`https://firebaseremoteconfig.googleapis.com/v1/projects/${t}/namespaces/fireperf:fetch?key=`+r,{method:"POST",headers:{Authorization:zt+" "+e},body:JSON.stringify({app_instance_id:a,app_instance_id_token:e,app_id:$t(n.app),app_version:Dt,sdk_version:Wt})});return fetch(t).then(e=>{if(e.ok)return e.json();throw C.create("RC response not ok")})}).catch(()=>{M.info(Gt)}).then(Jt).then(e=>{var t;e=e,t=k.getInstance().localStorage,e&&t&&(t.setItem(Ot,JSON.stringify(e)),t.setItem(Ft,String(Date.now()+60*N.getInstance().configTimeToLive*60*1e3)))},()=>{}))}let Gt="Could not fetch config, will use default configs";function Jt(e){var t,r;return e&&(t=N.getInstance(),void 0!==(r=e.entries||{}).fpr_enabled?t.loggingEnabled="true"===String(r.fpr_enabled):t.loggingEnabled=A.loggingEnabled,r.fpr_log_source?t.logSource=Number(r.fpr_log_source):A.logSource&&(t.logSource=A.logSource),r.fpr_log_endpoint_url?t.logEndPointUrl=r.fpr_log_endpoint_url:A.logEndPointUrl&&(t.logEndPointUrl=A.logEndPointUrl),r.fpr_log_transport_key?t.transportKey=r.fpr_log_transport_key:A.transportKey&&(t.transportKey=A.transportKey),void 0!==r.fpr_vc_network_request_sampling_rate?t.networkRequestsSamplingRate=Number(r.fpr_vc_network_request_sampling_rate):void 0!==A.networkRequestsSamplingRate&&(t.networkRequestsSamplingRate=A.networkRequestsSamplingRate),void 0!==r.fpr_vc_trace_sampling_rate?t.tracesSamplingRate=Number(r.fpr_vc_trace_sampling_rate):void 0!==A.tracesSamplingRate&&(t.tracesSamplingRate=A.tracesSamplingRate),r.fpr_log_max_flush_size?t.logMaxFlushSize=Number(r.fpr_log_max_flush_size):A.logMaxFlushSize&&(t.logMaxFlushSize=A.logMaxFlushSize),t.logTraceAfterSampling=Zt(t.tracesSamplingRate),t.logNetworkAfterSampling=Zt(t.networkRequestsSamplingRate)),e}function Zt(e){return Math.random()<=e}let Xt=1,Yt;function Qt(e){var r;return Xt=2,Yt=Yt||(r=e,(()=>{let r=k.getInstance().document;return new Promise(t=>{if(r&&"complete"!==r.readyState){let e=()=>{"complete"===r.readyState&&(r.removeEventListener("readystatechange",e),t())};r.addEventListener("readystatechange",e)}else t()})})().then(()=>{return e=r.installations,(t=e.getId()).then(e=>{L=e}),t;var e,t}).then(e=>Kt(r,e)).then(()=>er(),()=>er()))}function er(){Xt=3}let tr=1e4,rr=1e3,nr=3,ar=65536,ir=new TextEncoder,P=nr,R=[],or=!1;function sr(e){setTimeout(()=>{if(!(P<=0)){if(0<R.length){let e=R.splice(0,rr),t=cr(e);(e=>{var t=N.getInstance().getFlTransportFullUrl();return ir.encode(e).length<=ar&&navigator.sendBeacon&&navigator.sendBeacon(t,e)?Promise.resolve():fetch(t,{method:"POST",body:e})})(t).then(()=>{P=nr}).catch(()=>{R=[...e,...R],P--,M.info(`Tries left: ${P}.`),sr(tr)})}sr(tr)}},e)}function cr(e){var t=e.map(e=>({source_extension_json_proto3:e.message,event_time_ms:String(e.eventTime)})),t={request_time_ms:String(Date.now()),client_info:{client_type:1,js_client_info:{}},log_source:N.getInstance().logSource,log_event:t};return JSON.stringify(t)}function lr(t){return(...e)=>{e={message:t(...e),eventTime:Date.now()};if(!e.eventTime||!e.message)throw C.create("invalid cc log");R=[...R,e]}}function ur(){for(var e,t=N.getInstance().getFlTransportFullUrl();0<R.length;){var r=R.splice(-N.getInstance().logMaxFlushSize),n=cr(r);if(!navigator.sendBeacon||!navigator.sendBeacon(t,n)){R=[...R,...r];break}}0<R.length&&(e=cr(R),fetch(t,{method:"POST",body:e}).catch(()=>{M.info("Failed flushing queued events.")}))}let D;function dr(e,t){(D=D||{send:lr(gr),flush:ur}).send(e,t)}function pr(e){var t=N.getInstance();!t.instrumentationEnabled&&e.isAuto||(t.dataCollectionEnabled||e.isAuto)&&k.getInstance().requiredApisAvailable()&&(3===Xt?fr(e):Qt(e.performanceController).then(()=>fr(e),()=>fr(e)))}function fr(e){var t;L&&(t=N.getInstance()).loggingEnabled&&t.logTraceAfterSampling&&dr(e,1)}function gr(e,t){var r,n;return 0===t?(r={url:e.url,http_method:e.httpMethod||0,http_response_code:200,response_payload_bytes:e.responsePayloadBytes,client_start_time_us:e.startTimeUs,time_to_response_initiated_us:e.timeToResponseInitiatedUs,time_to_response_completed_us:e.timeToResponseCompletedUs},r={application_info:mr(e.performanceController.app),network_request_metric:r},JSON.stringify(r)):(r={name:(t=e).name,is_auto:t.isAuto,client_start_time_us:t.startTimeUs,duration_us:t.durationUs},0!==Object.keys(t.counters).length&&(r.counters=t.counters),n=t.getAttributes(),0!==Object.keys(n).length&&(r.custom_attributes=n),n={application_info:mr(t.performanceController.app),trace_metric:r},JSON.stringify(n))}function mr(e){return{google_app_id:$t(e),app_instance_id:L,web_app_info:{sdk_version:Dt,page_url:k.getInstance().getUrl(),service_worker_status:(t=k.getInstance().navigator)?.serviceWorker?t.serviceWorker.controller?2:3:1,visibility_state:(()=>{switch(k.getInstance().document.visibilityState){case"visible":return S.VISIBLE;case"hidden":return S.HIDDEN;default:return S.UNKNOWN}})(),effective_connection_type:(()=>{var e=k.getInstance().navigator.connection;switch(e&&e.effectiveType){case"slow-2g":return 1;case"2g":return 2;case"3g":return 3;case"4g":return 4;default:return 0}})()},application_process_state:0};var t}function hr(e,t){var r,n,a,i=t;i&&void 0!==i.responseStart&&(a=k.getInstance().getTimeOrigin(),a=Math.floor(1e3*(i.startTime+a)),r=i.responseStart?Math.floor(1e3*(i.responseStart-i.startTime)):void 0,n=Math.floor(1e3*(i.responseEnd-i.startTime)),i={performanceController:e,url:i.name&&i.name.split("?")[0],responsePayloadBytes:i.transferSize,startTimeUs:a,timeToResponseInitiatedUs:r,timeToResponseCompletedUs:n},t=i,(a=N.getInstance()).instrumentationEnabled)&&(r=t.url,n=a.logEndPointUrl.split("?")[0],i=a.flTransportEndpointUrl.split("?")[0],r!==n)&&r!==i&&a.loggingEnabled&&a.logNetworkAfterSampling&&dr(t,0)}let vr=["_fp","_fcp","_fid","_lcp","_cls","_inp"];class B{constructor(e,t,r=!1,n){this.performanceController=e,this.name=t,this.isAuto=r,this.state=1,this.customAttributes={},this.counters={},this.api=k.getInstance(),this.randomId=Math.floor(1e6*Math.random()),this.isAuto||(this.traceStartMark=`FB-PERF-TRACE-START-${this.randomId}-`+this.name,this.traceStopMark=`FB-PERF-TRACE-STOP-${this.randomId}-`+this.name,this.traceMeasure=n||`${Bt}-${this.randomId}-`+this.name,n&&this.calculateTraceMetrics())}start(){if(1!==this.state)throw C.create("trace started",{traceName:this.name});this.api.mark(this.traceStartMark),this.state=2}stop(){if(2!==this.state)throw C.create("trace stopped",{traceName:this.name});this.state=3,this.api.mark(this.traceStopMark),this.api.measure(this.traceMeasure,this.traceStartMark,this.traceStopMark),this.calculateTraceMetrics(),pr(this)}record(e,t,r){if(e<=0)throw C.create("nonpositive trace startTime",{traceName:this.name});if(t<=0)throw C.create("nonpositive trace duration",{traceName:this.name});if(this.durationUs=Math.floor(1e3*t),this.startTimeUs=Math.floor(1e3*e),r&&r.attributes&&(this.customAttributes={...r.attributes}),r&&r.metrics)for(var n of Object.keys(r.metrics))isNaN(Number(r.metrics[n]))||(this.counters[n]=Math.floor(Number(r.metrics[n])));pr(this)}incrementMetric(e,t=1){void 0===this.counters[e]?this.putMetric(e,t):this.putMetric(e,this.counters[e]+t)}putMetric(e,t){if(n=e,a=this.name,0===n.length||100<n.length||!(a&&a.startsWith("_wt_")&&-1<vr.indexOf(n))&&n.startsWith("_"))throw C.create("invalid custom metric name",{customMetricName:e});var r,n,a;this.counters[e]=(a=t??0,(r=Math.floor(a))<a&&M.info(`Metric value should be an Integer, setting the value as : ${r}.`),r)}getMetric(e){return this.counters[e]||0}putAttribute(e,t){var r,n,a=!(0===(r=e).length||40<r.length||Ht.some(e=>r.startsWith(e))||!r.match(Vt)),i=0!==(n=t).length&&n.length<=100;if(a&&i)this.customAttributes[e]=t;else{if(!a)throw C.create("invalid attribute name",{attributeName:e});if(!i)throw C.create("invalid attribute value",{attributeValue:t})}}getAttribute(e){return this.customAttributes[e]}removeAttribute(e){void 0!==this.customAttributes[e]&&delete this.customAttributes[e]}getAttributes(){return{...this.customAttributes}}setStartTime(e){this.startTimeUs=e}setDuration(e){this.durationUs=e}calculateTraceMetrics(){var e=this.api.getEntriesByName(this.traceMeasure),e=e&&e[0];e&&(this.durationUs=Math.floor(1e3*e.duration),this.startTimeUs=Math.floor(1e3*(e.startTime+this.api.getTimeOrigin())))}static createOobTrace(e,t,r,n,a){var i=k.getInstance().getUrl();if(i){var i=new B(e,"_wt_"+i,!0),o=Math.floor(1e3*k.getInstance().getTimeOrigin());i.setStartTime(o),t&&t[0]&&(i.setDuration(Math.floor(1e3*t[0].duration)),i.putMetric("domInteractive",Math.floor(1e3*t[0].domInteractive)),i.putMetric("domContentLoadedEventEnd",Math.floor(1e3*t[0].domContentLoadedEventEnd)),i.putMetric("loadEventEnd",Math.floor(1e3*t[0].loadEventEnd)));r&&((o=r.find(e=>"first-paint"===e.name))&&o.startTime&&i.putMetric("_fp",Math.floor(1e3*o.startTime)),(o=r.find(e=>"first-contentful-paint"===e.name))&&o.startTime&&i.putMetric("_fcp",Math.floor(1e3*o.startTime)),a)&&i.putMetric("_fid",Math.floor(1e3*a)),this.addWebVitalMetric(i,"_lcp","lcp_element",n.lcp),this.addWebVitalMetric(i,"_cls","cls_largestShiftTarget",n.cls),this.addWebVitalMetric(i,"_inp","inp_interactionTarget",n.inp),pr(i),D&&D.flush()}}static addWebVitalMetric(e,t,r,n){n&&(e.putMetric(t,Math.floor(1e3*n.value)),n.elementAttribution)&&e.putAttribute(r,n.elementAttribution)}static createUserTimingTrace(e,t){pr(new B(e,t,!1,t))}}let br={},yr=!1,wr;function Er(n){L&&(setTimeout(()=>{{var t=n;let e=k.getInstance();"onpagehide"in window?e.document.addEventListener("pagehide",()=>_r(t)):e.document.addEventListener("unload",()=>_r(t)),e.document.addEventListener("visibilitychange",()=>{"hidden"===e.document.visibilityState&&_r(t)}),e.onFirstInputDelay&&e.onFirstInputDelay(e=>{wr=e}),e.onLCP(e=>{br.lcp={value:e.value,elementAttribution:e.attribution?.element}}),e.onCLS(e=>{br.cls={value:e.value,elementAttribution:e.attribution?.largestShiftTarget}}),e.onINP(e=>{br.inp={value:e.value,elementAttribution:e.attribution?.interactionTarget}})}},0),setTimeout(()=>{var e,t=n,r=k.getInstance();for(e of r.getEntriesByType("resource"))hr(t,e);r.setupObserver("resource",e=>hr(t,e))},0),setTimeout(()=>{var e,t=n,r=k.getInstance();for(e of r.getEntriesByType("measure"))Tr(t,e);r.setupObserver("measure",e=>Tr(t,e))},0))}function Tr(e,t){var r=t.name;r.substring(0,Bt.length)!==Bt&&B.createUserTimingTrace(e,r)}function _r(r){if(!yr){yr=!0;var n=k.getInstance();let e=n.getEntriesByType("navigation"),t=n.getEntriesByType("paint");setTimeout(()=>{B.createOobTrace(r,e,t,br,wr)},0)}}class Sr{constructor(e,t){this.app=e,this.installations=t,this.initialized=!1}_init(e){this.initialized||(void 0!==e?.dataCollectionEnabled&&(this.dataCollectionEnabled=e.dataCollectionEnabled),void 0!==e?.instrumentationEnabled&&(this.instrumentationEnabled=e.instrumentationEnabled),k.getInstance().requiredApisAvailable()?new Promise((n,a)=>{try{let e=!0,t="validate-browser-context-for-indexeddb-analytics-module",r=self.indexedDB.open(t);r.onsuccess=()=>{r.result.close(),e||self.indexedDB.deleteDatabase(t),n(!0)},r.onupgradeneeded=()=>{e=!1},r.onerror=()=>{a(r.error?.message||"")}}catch(e){a(e)}}).then(e=>{e&&(or||(sr(5500),or=!0),Qt(this).then(()=>Er(this),()=>Er(this)),this.initialized=!0)}).catch(e=>{M.info("Environment doesn't support IndexedDB: "+e)}):M.info('Firebase Performance cannot start if the browser does not support "Fetch" and "Promise", or cookies are disabled.'))}set instrumentationEnabled(e){N.getInstance().instrumentationEnabled=e}get instrumentationEnabled(){return N.getInstance().instrumentationEnabled}set dataCollectionEnabled(e){N.getInstance().dataCollectionEnabled=e}get dataCollectionEnabled(){return N.getInstance().dataCollectionEnabled}}let Ir="[DEFAULT]";let Cr=(e,{options:t})=>{var r=e.getProvider("app").getImmediate(),n=e.getProvider("installations-internal").getImmediate();if(r.name!==Ir)throw C.create("FB not default");if("undefined"==typeof window)throw C.create("no window");e=window,Ut=e;r=new Sr(r,n);return r._init(t),r};Nr._registerComponent(new e("performance",Cr,"PUBLIC")),Nr.registerVersion(Pt,Rt),Nr.registerVersion(Pt,Rt,"esm2020");class Mr{constructor(e,t){this.app=e,this._delegate=t}get instrumentationEnabled(){return this._delegate.instrumentationEnabled}set instrumentationEnabled(e){this._delegate.instrumentationEnabled=e}get dataCollectionEnabled(){return this._delegate.dataCollectionEnabled}set dataCollectionEnabled(e){this._delegate.dataCollectionEnabled=e}trace(e){return t=this._delegate,e=e,t=(r=t)&&r._delegate?r._delegate:r,new B(t,e);var t,r}}function kr(e){var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("performance").getImmediate();return new Mr(t,r)}(I=F.default).INTERNAL.registerComponent(new e("performance-compat",kr,"PUBLIC")),I.registerVersion("@firebase/performance-compat","0.2.21")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-performance-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-performance-compat.js.map
