import{_removeServiceInstance as e,getApp as t,_getProvider,_registerComponent as n,registerVersion as r,_isFirebaseServerApp as i,SDK_VERSION as o}from"https://www.gstatic.com/firebasejs/12.0.0/firebase-app.js";function isCloudWorkstation(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch{return!1}}const s={};let a=!1;function updateEmulatorBanner(e,t){if("undefined"==typeof window||"undefined"==typeof document||!isCloudWorkstation(window.location.host)||s[e]===t||s[e]||a)return;function prefixedId(e){return`__firebase__banner__${e}`}s[e]=t;const n="__firebase__banner",r=function getEmulatorSummary(){const e={prod:[],emulator:[]};for(const t of Object.keys(s))s[t]?e.emulator.push(t):e.prod.push(t);return e}().prod.length>0;function setupCloseBtn(){const e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{a=!0,function tearDown(){const e=document.getElementById(n);e&&e.remove()}()},e}function setupDom(){const e=function getOrCreateEl(e){let t=document.getElementById(e),n=!1;return t||(t=document.createElement("div"),t.setAttribute("id",e),n=!0),{created:n,element:t}}(n),t=prefixedId("text"),i=document.getElementById(t)||document.createElement("span"),o=prefixedId("learnmore"),s=document.getElementById(o)||document.createElement("a"),a=prefixedId("preprendIcon"),c=document.getElementById(a)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(e.created){const t=e.element;!function setupBannerStyles(e){e.style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center"}(t),function setupLinkStyles(e,t){e.setAttribute("id",t),e.innerText="Learn more",e.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",e.setAttribute("target","__blank"),e.style.paddingLeft="5px",e.style.textDecoration="underline"}(s,o);const n=setupCloseBtn();!function setupIconStyles(e,t){e.setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px"}(c,a),t.append(c,i,s,n),document.body.appendChild(t)}r?(i.innerText="Preview backend disconnected.",c.innerHTML='<g clip-path="url(#clip0_6013_33858)">\n<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6013_33858">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>'):(c.innerHTML='<g clip-path="url(#clip0_6083_34804)">\n<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>\n</g>\n<defs>\n<clipPath id="clip0_6083_34804">\n<rect width="24" height="24" fill="white"/>\n</clipPath>\n</defs>',i.innerText="Preview backend running in this workspace."),i.setAttribute("id",t)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",setupDom):setupDom()}class FirebaseError extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){const n=t[0]||{},r=`${this.service}/${e}`,i=this.errors[e],o=i?function replaceTemplate(e,t){return e.replace(c,((e,n)=>{const r=t[n];return null!=r?String(r):`<${n}?>`}))}(i,n):"Error",s=`${this.serviceName}: ${o} (${r}).`;return new FirebaseError(r,s,n)}}const c=/\{\$([^}]+)}/g;class Component{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var h;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(h||(h={}));const l={debug:h.DEBUG,verbose:h.VERBOSE,info:h.INFO,warn:h.WARN,error:h.ERROR,silent:h.SILENT},u=h.INFO,p={[h.DEBUG]:"log",[h.VERBOSE]:"log",[h.INFO]:"info",[h.WARN]:"warn",[h.ERROR]:"error"},defaultLogHandler=(e,t,...n)=>{if(t<e.logLevel)return;const r=(new Date).toISOString(),i=p[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${r}]  ${e.name}:`,...n)};const d="@firebase/data-connect",g="0.3.11";let f="";class AppCheckTokenProvider{constructor(e,t){this.appCheckProvider=t,i(e)&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.appCheck=t?.getImmediate({optional:!0}),this.appCheck||t?.get().then((e=>this.appCheck=e)).catch()}getToken(){return this.serverAppAppCheckToken?Promise.resolve({token:this.serverAppAppCheckToken}):this.appCheck?this.appCheck.getToken():new Promise(((e,t)=>{setTimeout((()=>{this.appCheck?this.getToken().then(e,t):e(null)}),0)}))}addTokenChangeListener(e){this.appCheckProvider?.get().then((t=>t.addTokenListener(e)))}}const _={OTHER:"other",ALREADY_INITIALIZED:"already-initialized",NOT_INITIALIZED:"not-initialized",NOT_SUPPORTED:"not-supported",INVALID_ARGUMENT:"invalid-argument",PARTIAL_ERROR:"partial-error",UNAUTHORIZED:"unauthorized"};class DataConnectError extends FirebaseError{constructor(e,t){super(e,t),this.name="DataConnectError",Object.setPrototypeOf(this,DataConnectError.prototype)}toString(){return`${this.name}[code=${this.code}]: ${this.message}`}}class DataConnectOperationError extends DataConnectError{constructor(e,t){super(_.PARTIAL_ERROR,e),this.name="DataConnectOperationError",this.response=t}}const C=new class Logger{constructor(e){this.name=e,this._logLevel=u,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in h))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?l[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,h.DEBUG,...e),this._logHandler(this,h.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,h.VERBOSE,...e),this._logHandler(this,h.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,h.INFO,...e),this._logHandler(this,h.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,h.WARN,...e),this._logHandler(this,h.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,h.ERROR,...e),this._logHandler(this,h.ERROR,...e)}}("@firebase/data-connect");function setLogLevel(e){C.setLogLevel(e)}function logDebug(e){C.debug(`DataConnect (${f}): ${e}`)}function logError(e){C.error(`DataConnect (${f}): ${e}`)}class FirebaseAuthProvider{constructor(e,t,n){this._appName=e,this._options=t,this._authProvider=n,this._auth=n.getImmediate({optional:!0}),this._auth||n.onInit((e=>this._auth=e))}getToken(e){return this._auth?this._auth.getToken(e).catch((e=>e&&"auth/token-not-initialized"===e.code?(logDebug("Got auth/token-not-initialized error.  Treating as null token."),null):(logError("Error received when attempting to retrieve token: "+JSON.stringify(e)),Promise.reject(e)))):new Promise(((t,n)=>{setTimeout((()=>{this._auth?this.getToken(e).then(t,n):t(null)}),0)}))}addTokenChangeListener(e){this._auth?.addAuthTokenListener(e)}removeTokenChangeListener(e){this._authProvider.get().then((t=>t.removeAuthTokenListener(e))).catch((e=>logError(e)))}}const m="query",E="mutation",k="SERVER",T="CACHE";let v;function getRefSerializer(e,t,n){return function toJSON(){return{data:t,refInfo:{name:e.name,variables:e.variables,connectorConfig:{projectId:e.dataConnect.app.options.projectId,...e.dataConnect.getSettings()}},fetchTime:Date.now().toLocaleString(),source:n}}}!function setEncoder(e){v=e}((e=>JSON.stringify(e)));class QueryManager{constructor(e){this.transport=e,this._queries=new Map}track(e,t,n){const r={name:e,variables:t,refType:m},i=v(r),o={ref:r,subscriptions:[],currentCache:n||null,lastError:null};return function setIfNotExists(e,t,n){e.has(t)||e.set(t,n)}(this._queries,i,o),this._queries.get(i)}addSubscription(e,t,n,r){const i=v({name:e.name,variables:e.variables,refType:m}),o=this._queries.get(i),s={userCallback:t,errCallback:n},unsubscribe=()=>{const e=this._queries.get(i);e.subscriptions=e.subscriptions.filter((e=>e!==s))};if(r&&o.currentCache!==r&&(logDebug("Initial cache found. Comparing dates."),(!o.currentCache||o.currentCache&&function compareDates(e,t){const n=new Date(e),r=new Date(t);return n.getTime()<r.getTime()}(o.currentCache.fetchTime,r.fetchTime))&&(o.currentCache=r)),null!==o.currentCache){t({data:o.currentCache.data,source:T,ref:e,toJSON:getRefSerializer(e,o.currentCache.data,T),fetchTime:o.currentCache.fetchTime}),null!==o.lastError&&n&&n(void 0)}if(o.subscriptions.push({userCallback:t,errCallback:n,unsubscribe:unsubscribe}),!o.currentCache){logDebug(`No cache available for query ${e.name} with variables ${JSON.stringify(e.variables)}. Calling executeQuery.`);this.executeQuery(e).then(void 0,(e=>{}))}return unsubscribe}executeQuery(e){if(e.refType!==m)throw new DataConnectError(_.INVALID_ARGUMENT,"ExecuteQuery can only execute query operation");const t=v({name:e.name,variables:e.variables,refType:m}),n=this._queries.get(t);return this.transport.invokeQuery(e.name,e.variables).then((t=>{const r=(new Date).toString(),i={...t,source:k,ref:e,toJSON:getRefSerializer(e,t.data,k),fetchTime:r};return n.subscriptions.forEach((e=>{e.userCallback(i)})),n.currentCache={data:t.data,source:T,fetchTime:r},i}),(e=>{throw n.lastError=e,n.subscriptions.forEach((t=>{t.errCallback&&t.errCallback(e)})),e}))}enableEmulator(e,t){this.transport.useEmulator(e,t)}}const y={Base:"Base",Generated:"Generated",TanstackReactCore:"TanstackReactCore",GeneratedReact:"GeneratedReact",TanstackAngularCore:"TanstackAngularCore",GeneratedAngular:"GeneratedAngular"};function addToken(e,t){if(!t)return e;const n=new URL(e);return n.searchParams.append("key",t),n.toString()}let b=globalThis.fetch;function getGoogApiClientValue(e,t){let n="gl-js/ fire/"+f;return t!==y.Base&&t!==y.Generated?n+=" js/"+t.toLowerCase():(e||t===y.Generated)&&(n+=" js/gen"),n}function dcFetch(e,t,{signal:n},r,i,o,s,a,c){if(!b)throw new DataConnectError(_.OTHER,"No Fetch Implementation detected!");const h={"Content-Type":"application/json","X-Goog-Api-Client":getGoogApiClientValue(s,a)};i&&(h["X-Firebase-Auth-Token"]=i),r&&(h["x-firebase-gmpid"]=r),o&&(h["X-Firebase-AppCheck"]=o);const l={body:JSON.stringify(t),method:"POST",headers:h,signal:n};return isCloudWorkstation(e)&&c&&(l.credentials="include"),b(e,l).catch((e=>{throw new DataConnectError(_.OTHER,"Failed to fetch: "+JSON.stringify(e))})).then((async e=>{let t=null;try{t=await e.json()}catch(e){throw new DataConnectError(_.OTHER,JSON.stringify(e))}const n=function getMessage(e){if("message"in e)return e.message;return JSON.stringify(e)}(t);if(e.status>=400){if(logError("Error while performing request: "+JSON.stringify(t)),401===e.status)throw new DataConnectError(_.UNAUTHORIZED,n);throw new DataConnectError(_.OTHER,n)}return t})).then((e=>{if(e.errors&&e.errors.length){const t=JSON.stringify(e.errors),n={errors:e.errors,data:e.data};throw new DataConnectOperationError("DataConnect error while performing request: "+t,n)}return e}))}class RESTTransport{constructor(e,t,n,r,i,o,s=!1,a=y.Base){this.apiKey=t,this.appId=n,this.authProvider=r,this.appCheckProvider=i,this._isUsingGen=s,this._callerSdkType=a,this._host="",this._location="l",this._connectorName="",this._secure=!0,this._project="p",this._accessToken=null,this._appCheckToken=null,this._lastToken=null,this._isUsingEmulator=!1,this.invokeQuery=(e,t)=>{const n=new AbortController;return this.withRetry((()=>dcFetch(addToken(`${this.endpointUrl}:executeQuery`,this.apiKey),{name:`projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,operationName:e,variables:t},n,this.appId,this._accessToken,this._appCheckToken,this._isUsingGen,this._callerSdkType,this._isUsingEmulator)))},this.invokeMutation=(e,t)=>{const n=new AbortController;return this.withRetry((()=>dcFetch(addToken(`${this.endpointUrl}:executeMutation`,this.apiKey),{name:`projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,operationName:e,variables:t},n,this.appId,this._accessToken,this._appCheckToken,this._isUsingGen,this._callerSdkType,this._isUsingEmulator)))},o&&("number"==typeof o.port&&(this._port=o.port),void 0!==o.sslEnabled&&(this._secure=o.sslEnabled),this._host=o.host);const{location:c,projectId:h,connector:l,service:u}=e;if(c&&(this._location=c),h&&(this._project=h),this._serviceName=u,!l)throw new DataConnectError(_.INVALID_ARGUMENT,"Connector Name required!");this._connectorName=l,this.authProvider?.addTokenChangeListener((e=>{logDebug(`New Token Available: ${e}`),this._accessToken=e})),this.appCheckProvider?.addTokenChangeListener((e=>{const{token:t}=e;logDebug(`New App Check Token Available: ${t}`),this._appCheckToken=t}))}get endpointUrl(){return function urlBuilder(e,t){const{connector:n,location:r,projectId:i,service:o}=e,{host:s,sslEnabled:a,port:c}=t;let h=`${a?"https":"http"}://${s||"firebasedataconnect.googleapis.com"}`;if("number"==typeof c)h+=`:${c}`;else if(void 0!==c)throw logError("Port type is of an invalid type"),new DataConnectError(_.INVALID_ARGUMENT,"Incorrect type for port passed in!");return`${h}/v1/projects/${i}/locations/${r}/services/${o}/connectors/${n}`}({connector:this._connectorName,location:this._location,projectId:this._project,service:this._serviceName},{host:this._host,sslEnabled:this._secure,port:this._port})}useEmulator(e,t,n){this._host=e,this._isUsingEmulator=!0,"number"==typeof t&&(this._port=t),void 0!==n&&(this._secure=n)}onTokenChanged(e){this._accessToken=e}async getWithAuth(e=!1){let t=new Promise((e=>e(this._accessToken)));return this.appCheckProvider&&(this._appCheckToken=(await this.appCheckProvider.getToken())?.token),t=this.authProvider?this.authProvider.getToken(e).then((e=>e?(this._accessToken=e.accessToken,this._accessToken):null)):new Promise((e=>e(""))),t}_setLastToken(e){this._lastToken=e}withRetry(e,t=!1){let n=!1;return this.getWithAuth(t).then((e=>(n=this._lastToken!==e,this._lastToken=e,e))).then(e).catch((r=>{if("code"in r&&r.code===_.UNAUTHORIZED&&!t&&n)return logDebug("Retrying due to unauthorized"),this.withRetry(e,!0);throw r}))}_setCallerSdkType(e){this._callerSdkType=e}}function mutationRef(e,t,n){e.setInitialized();return{dataConnect:e,name:t,refType:E,variables:n}}class MutationManager{constructor(e){this._transport=e,this._inflight=[]}executeMutation(e){const t=this._transport.invokeMutation(e.name,e.variables),n=t.then((t=>({...t,source:k,ref:e,fetchTime:Date.now().toLocaleString()})));this._inflight.push(t);const removePromise=()=>this._inflight=this._inflight.filter((e=>e!==t));return t.then(removePromise,removePromise),n}}function executeMutation(e){return e.dataConnect._mutationManager.executeMutation(e)}function parseOptions(e){const[t,n]=e.split("://"),r="https"===t,[i,o]=n.split(":");return{host:i,port:Number(o),sslEnabled:r}}class DataConnect{constructor(e,t,n,r){if(this.app=e,this.dataConnectOptions=t,this._authProvider=n,this._appCheckProvider=r,this.isEmulator=!1,this._initialized=!1,this._isUsingGeneratedSdk=!1,this._callerSdkType=y.Base,"undefined"!=typeof process&&process.env){const e=process.env.FIREBASE_DATA_CONNECT_EMULATOR_HOST;e&&(logDebug("Found custom host. Using emulator"),this.isEmulator=!0,this._transportOptions=parseOptions(e))}}_useGeneratedSdk(){this._isUsingGeneratedSdk||(this._isUsingGeneratedSdk=!0)}_setCallerSdkType(e){this._callerSdkType=e,this._initialized&&this._transport._setCallerSdkType(e)}_delete(){return e(this.app,"data-connect",JSON.stringify(this.getSettings())),Promise.resolve()}getSettings(){const e=JSON.parse(JSON.stringify(this.dataConnectOptions));return delete e.projectId,e}setInitialized(){this._initialized||(void 0===this._transportClass&&(logDebug("transportClass not provided. Defaulting to RESTTransport."),this._transportClass=RESTTransport),this._authProvider&&(this._authTokenProvider=new FirebaseAuthProvider(this.app.name,this.app.options,this._authProvider)),this._appCheckProvider&&(this._appCheckTokenProvider=new AppCheckTokenProvider(this.app,this._appCheckProvider)),this._initialized=!0,this._transport=new this._transportClass(this.dataConnectOptions,this.app.options.apiKey,this.app.options.appId,this._authTokenProvider,this._appCheckTokenProvider,void 0,this._isUsingGeneratedSdk,this._callerSdkType),this._transportOptions&&this._transport.useEmulator(this._transportOptions.host,this._transportOptions.port,this._transportOptions.sslEnabled),this._queryManager=new QueryManager(this._transport),this._mutationManager=new MutationManager(this._transport))}enableEmulator(e){if(this._initialized&&!areTransportOptionsEqual(this._transportOptions,e))throw logError("enableEmulator called after initialization"),new DataConnectError(_.ALREADY_INITIALIZED,"DataConnect instance already initialized!");this._transportOptions=e,this.isEmulator=!0}}function areTransportOptionsEqual(e,t){return e.host===t.host&&e.port===t.port&&e.sslEnabled===t.sslEnabled}function connectDataConnectEmulator(e,t,n,r=!1){isCloudWorkstation(t)&&(!async function pingServer(e){return(await fetch(e,{credentials:"include"})).ok}(`https://${t}${n?`:${n}`:""}`),updateEmulatorBanner("Data Connect",!0)),e.enableEmulator({host:t,port:n,sslEnabled:r})}function getDataConnect(e,n){let r,i;"location"in e?(i=e,r=t()):(i=n,r=e),r&&0!==Object.keys(r).length||(r=t());const o=_getProvider(r,"data-connect"),s=JSON.stringify(i);if(o.isInitialized(s)){const e=o.getImmediate({identifier:s}),t=o.getOptions(s);if(Object.keys(t).length>0)return logDebug("Re-using cached instance"),e}return validateDCOptions(i),logDebug("Creating new DataConnect instance"),o.initialize({instanceIdentifier:s,options:i})}function validateDCOptions(e){if(!e)throw new DataConnectError(_.INVALID_ARGUMENT,"DC Option Required");return["connector","location","service"].forEach((t=>{if(null===e[t]||void 0===e[t])throw new DataConnectError(_.INVALID_ARGUMENT,`${t} Required`)})),!0}function terminate(e){return e._delete()}function executeQuery(e){return e.dataConnect._queryManager.executeQuery(e)}function queryRef(e,t,n,r){return e.setInitialized(),e._queryManager.track(t,n,r),{dataConnect:e,refType:m,name:t,variables:n}}function toQueryRef(e){const{refInfo:{name:t,variables:n,connectorConfig:r}}=e;return queryRef(getDataConnect(r),t,n)}function validateArgs(e,t,n,r){let i,o;if(t&&"enableEmulator"in t?(i=t,o=n):(i=getDataConnect(e),o=t),!i||!o&&r)throw new DataConnectError(_.INVALID_ARGUMENT,"Variables required.");return{dc:i,vars:o}}function subscribe(e,t,n,r){let i,o,s;if("refInfo"in e){const t=e,{data:n,source:r,fetchTime:s}=t;o={data:n,source:r,fetchTime:s},i=toQueryRef(t)}else i=e;if("function"==typeof t?s=t:(s=t.onNext,n=t.onErr,t.onComplete),!s)throw new DataConnectError(_.INVALID_ARGUMENT,"Must provide onNext");return i.dataConnect._queryManager.addSubscription(i,s,n,o)}!function registerDataConnect(e){!function setSDKVersion(e){f=e}(o),n(new Component("data-connect",((e,{instanceIdentifier:t,options:n})=>{const r=e.getProvider("app").getImmediate(),i=e.getProvider("auth-internal"),o=e.getProvider("app-check-internal");let s=n;if(t&&(s=JSON.parse(t)),!r.options.projectId)throw new DataConnectError(_.INVALID_ARGUMENT,"Project ID must be provided. Did you pass in a proper projectId to initializeApp?");return new DataConnect(r,{...s,projectId:r.options.projectId},i,o)}),"PUBLIC").setMultipleInstances(!0)),r(d,g,e),r(d,g,"esm2020")}();export{y as CallerSdkTypeEnum,_ as Code,DataConnect,DataConnectError,DataConnectOperationError,E as MUTATION_STR,MutationManager,m as QUERY_STR,T as SOURCE_CACHE,k as SOURCE_SERVER,areTransportOptionsEqual,connectDataConnectEmulator,executeMutation,executeQuery,getDataConnect,mutationRef,parseOptions,queryRef,setLogLevel,subscribe,terminate,toQueryRef,validateArgs,validateDCOptions};

//# sourceMappingURL=firebase-data-connect.js.map
