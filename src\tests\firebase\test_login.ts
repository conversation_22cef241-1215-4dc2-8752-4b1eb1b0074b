// Test login function
import { signInWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../../config/firebase';

const testLogin = async () => {
  try {
    const userCredential = await signInWithEmailAndPassword(
      auth, 
      '<EMAIL>', 
      'password123'
    );
    console.log('Login successful:', userCredential.user.uid);
  } catch (error) {
    console.error('Login failed:', error);
  }
};

// Call the testFirestore function to use it
testLogin();